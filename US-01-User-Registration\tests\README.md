# US-01 User Registration - Testing Documentation

## Overview
This folder contains comprehensive testing documentation and scripts for the US-01 User Registration microservice. It explains the purpose of every component and provides detailed testing instructions.

## Folder Structure Explanation

### `/backend` Folder
**Purpose**: Contains the main FastAPI application that handles all authentication logic
- **app.py**: Main application file with all API endpoints
- **requirements.txt**: Python dependencies needed to run the service
- **Functions**: Registration, login, email verification, password hashing, JWT token management

### `/database` Folder  
**Purpose**: Manages all database operations and schema
- **init_db.py**: Database initialization script that creates tables and indexes
- **users.db**: SQLite database file (created automatically)
- **Schema**: Users table with email, password hash, verification status, and timestamps

### `/docs` Folder
**Purpose**: Contains detailed documentation explaining every line of code
- **README.md**: Complete API documentation with line-by-line explanations
- **Endpoint details**: Request/response formats, error codes, security features
- **Integration guide**: How frontend should interact with the API

### `/frontend` Folder
**Purpose**: React components for user interface
- **RegisterForm.jsx**: User registration form with validation
- **LoginForm.jsx**: User login form with JWT token handling
- **Features**: Client-side validation, error handling, responsive design

### `/tests` Folder
**Purpose**: Testing scripts and documentation
- **test_endpoints.py**: Automated test suite for all endpoints
- **README.md**: This file - explains testing procedures and folder purposes

## Testing Procedures

### 1. Automated Testing
Run the automated test suite to verify all endpoints:

```bash
cd US-01-User-Registration/tests
python test_endpoints.py
```

**What it tests**:
- Health check endpoint
- User registration with valid data
- Email validation
- Password strength requirements
- Duplicate email prevention
- Login with unverified account
- Email verification process
- Login with verified account
- Invalid credential handling

### 2. Manual Testing Steps

#### Step 1: Start the Backend Service
```bash
cd US-01-User-Registration/backend
pip install -r requirements.txt
python app.py
```
Service will start on `http://localhost:8001`

#### Step 2: Initialize Database
```bash
cd US-01-User-Registration/database
python init_db.py
```

#### Step 3: Test Each Endpoint

**Health Check**:
```bash
curl http://localhost:8001/health
```
Expected: `{"status": "healthy", "service": "US-01 User Registration"}`

**User Registration**:
```bash
curl -X POST http://localhost:8001/register \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "password": "TestPass123!",
       "confirm_password": "TestPass123!"
     }'
```
Expected: Success message with user_id

**Login (Unverified)**:
```bash
curl -X POST http://localhost:8001/login \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "password": "TestPass123!"
     }'
```
Expected: 401 error - Email not verified

**Email Verification**:
1. Check database for verification_token
2. Visit: `http://localhost:8001/verify-email?token=<token>`
Expected: "Email verified successfully"

**Login (Verified)**:
```bash
curl -X POST http://localhost:8001/login \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "password": "TestPass123!"
     }'
```
Expected: JWT token in response

### 3. Frontend Testing

#### Setup Frontend Environment
```bash
cd US-01-User-Registration/frontend
npm install
npm start
```

#### Test Registration Form
1. Open `http://localhost:3000/register`
2. Test form validation:
   - Invalid email formats
   - Weak passwords
   - Password mismatches
3. Submit valid registration
4. Verify success message

#### Test Login Form
1. Open `http://localhost:3000/login`
2. Test with unverified account
3. Verify email and test again
4. Check JWT token storage

## Error Testing Scenarios

### 1. Validation Errors
- **Invalid email**: `test@` or `invalid-email`
- **Weak password**: `123` or `password`
- **Password mismatch**: Different confirm_password
- **Missing fields**: Empty email or password

### 2. Business Logic Errors
- **Duplicate registration**: Same email twice
- **Unverified login**: Login before email verification
- **Invalid credentials**: Wrong password
- **Non-existent user**: Login with unregistered email

### 3. Security Testing
- **SQL Injection**: Try malicious input in email/password
- **XSS**: Test script tags in form fields
- **Token validation**: Use invalid JWT tokens
- **Rate limiting**: Multiple rapid requests

## Database Testing

### Check User Data
```sql
-- Connect to database
sqlite3 US-01-User-Registration/database/users.db

-- View all users
SELECT id, email, is_verified, created_at FROM users;

-- Check verification tokens
SELECT email, verification_token FROM users WHERE is_verified = FALSE;

-- View password hashes (should be bcrypt)
SELECT email, password_hash FROM users;
```

### Reset Database for Testing
```bash
cd US-01-User-Registration/database
python init_db.py
# Choose 'y' when prompted to reset
```

## Performance Testing

### Load Testing
Use tools like Apache Bench or curl to test concurrent requests:

```bash
# Test registration endpoint
ab -n 100 -c 10 -p registration_data.json -T application/json http://localhost:8001/register

# Test login endpoint  
ab -n 100 -c 10 -p login_data.json -T application/json http://localhost:8001/login
```

### Response Time Testing
Monitor response times for each endpoint:
- Registration: Should be < 500ms
- Login: Should be < 200ms
- Email verification: Should be < 100ms
- Health check: Should be < 50ms

## Integration Testing

### Frontend-Backend Integration
1. Start both frontend and backend services
2. Test complete user flow:
   - Registration → Email verification → Login → Dashboard
3. Verify JWT token handling
4. Test error message display

### Email Service Integration
1. Configure SMTP settings in environment variables
2. Test actual email sending
3. Verify email templates and formatting
4. Test email delivery and verification links

## Troubleshooting Common Issues

### Database Issues
- **Permission errors**: Check file permissions on database directory
- **Lock errors**: Ensure no other processes are using the database
- **Schema errors**: Run init_db.py to recreate tables

### Email Issues
- **SMTP errors**: Check email configuration in environment variables
- **Authentication errors**: Verify app passwords for Gmail
- **Delivery issues**: Check spam folders and email formatting

### API Issues
- **CORS errors**: Verify CORS middleware configuration
- **Validation errors**: Check Pydantic model definitions
- **Authentication errors**: Verify JWT secret key and token format

## Testing Checklist

Before deploying to production, ensure all tests pass:

- [ ] All automated tests pass
- [ ] Manual endpoint testing completed
- [ ] Frontend forms work correctly
- [ ] Email verification functions
- [ ] JWT tokens are properly generated and validated
- [ ] Error handling works for all scenarios
- [ ] Database operations are secure
- [ ] Password hashing is working
- [ ] Input validation prevents malicious input
- [ ] API documentation is accessible
- [ ] Performance meets requirements
- [ ] Integration with frontend is seamless
