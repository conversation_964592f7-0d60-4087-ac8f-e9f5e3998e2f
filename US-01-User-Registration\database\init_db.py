"""
Database initialization script for US-01 User Registration
Creates and sets up the SQLite database with required tables
"""

import sqlite3
import os
from datetime import datetime

def create_database():
    """
    Create the SQLite database and users table
    This function sets up the complete database schema for user registration
    """
    
    # Ensure database directory exists
    os.makedirs(os.path.dirname(__file__), exist_ok=True)
    
    # Connect to SQLite database (creates file if doesn't exist)
    db_path = os.path.join(os.path.dirname(__file__), 'users.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create users table with all required fields
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id TEXT PRIMARY KEY,                    -- Unique user identifier (UUID)
            email TEXT UNIQUE NOT NULL,             -- User email address (unique)
            password_hash TEXT NOT NULL,            -- Bcrypt hashed password
            is_verified BOOLEAN DEFAULT FALSE,      -- Email verification status
            verification_token TEXT,                -- Token for email verification
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- Account creation time
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP   -- Last update time
        )
    ''')
    
    # Create index on email for faster lookups
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)
    ''')
    
    # Create index on verification token for faster verification
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_users_verification_token ON users(verification_token)
    ''')
    
    # Commit changes and close connection
    conn.commit()
    conn.close()
    
    print(f"Database created successfully at: {db_path}")

def reset_database():
    """
    Reset the database by dropping and recreating all tables
    WARNING: This will delete all existing data
    """
    
    db_path = os.path.join(os.path.dirname(__file__), 'users.db')
    
    # Remove existing database file if it exists
    if os.path.exists(db_path):
        os.remove(db_path)
        print("Existing database removed")
    
    # Create fresh database
    create_database()
    print("Database reset completed")

def check_database_status():
    """
    Check if database exists and show table information
    """
    
    db_path = os.path.join(os.path.dirname(__file__), 'users.db')
    
    if not os.path.exists(db_path):
        print("Database does not exist")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check if users table exists
    cursor.execute('''
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='users'
    ''')
    
    if cursor.fetchone():
        # Get table info
        cursor.execute('PRAGMA table_info(users)')
        columns = cursor.fetchall()
        
        print("Database exists and users table is present")
        print("Table structure:")
        for column in columns:
            print(f"  - {column[1]} ({column[2]})")
        
        # Get user count
        cursor.execute('SELECT COUNT(*) FROM users')
        user_count = cursor.fetchone()[0]
        print(f"Total users: {user_count}")
        
        conn.close()
        return True
    else:
        print("Database exists but users table is missing")
        conn.close()
        return False

if __name__ == "__main__":
    """
    Run this script directly to initialize the database
    """
    
    print("US-01 User Registration Database Setup")
    print("=" * 40)
    
    # Check current status
    print("Checking current database status...")
    exists = check_database_status()
    
    if not exists:
        print("\nCreating new database...")
        create_database()
        print("Database setup completed!")
    else:
        print("\nDatabase already exists and is properly configured.")
        
        # Ask if user wants to reset
        response = input("\nDo you want to reset the database? (y/N): ")
        if response.lower() in ['y', 'yes']:
            reset_database()
            print("Database reset completed!")
    
    print("\nFinal database status:")
    check_database_status()
