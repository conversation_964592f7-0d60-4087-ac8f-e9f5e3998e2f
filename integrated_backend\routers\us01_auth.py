"""
US-01: User Registration Router
Handles user registration and email verification
"""

from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel, EmailStr, field_validator
import bcrypt
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import os
from datetime import datetime
import re
import uuid
from typing import Optional

from database.database import get_db_connection

router = APIRouter()

# Configuration
EMAIL_ADDRESS = os.getenv("EMAIL_ADDRESS", "<EMAIL>")
EMAIL_PASSWORD = os.getenv("EMAIL_PASSWORD", "your-app-password")
SMTP_SERVER = os.getenv("SMTP_SERVER", "smtp.gmail.com")
SMTP_PORT = int(os.getenv("SMTP_PORT", "587"))

# Pydantic models
class UserRegistration(BaseModel):
    email: EmailStr
    password: str
    confirm_password: str
    
    @field_validator('password')
    @classmethod
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain at least one special character')
        return v
    
    @field_validator('confirm_password')
    @classmethod
    def passwords_match(cls, v, info):
        if 'password' in info.data and v != info.data['password']:
            raise ValueError('Passwords do not match')
        return v

class User(BaseModel):
    id: str
    email: str
    is_verified: bool
    created_at: datetime

# Utility functions
def hash_password(password: str) -> str:
    """Hash password using bcrypt"""
    salt = bcrypt.gensalt()
    return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')

def send_verification_email(email: str, verification_token: str):
    """Send email verification"""
    try:
        msg = MIMEMultipart()
        msg['From'] = EMAIL_ADDRESS
        msg['To'] = email
        msg['Subject'] = "Verify Your Email - OpenResume AI"
        
        verification_link = f"http://localhost:8000/api/v1/auth/verify-email?token={verification_token}"
        
        body = f"""
        Welcome to OpenResume AI!
        
        Please click the link below to verify your email address:
        {verification_link}
        
        If you didn't create an account, please ignore this email.
        
        Best regards,
        OpenResume AI Team
        """
        
        msg.attach(MIMEText(body, 'plain'))
        
        server = smtplib.SMTP(SMTP_SERVER, SMTP_PORT)
        server.starttls()
        server.login(EMAIL_ADDRESS, EMAIL_PASSWORD)
        text = msg.as_string()
        server.sendmail(EMAIL_ADDRESS, email, text)
        server.quit()
        
        return True
    except Exception as e:
        print(f"Failed to send email: {e}")
        return False

# API Endpoints
@router.post("/register", response_model=dict)
async def register_user(user_data: UserRegistration):
    """
    Register a new user
    - Validates email and password
    - Checks if user already exists
    - Hashes password
    - Sends verification email
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # Check if user already exists
        cursor.execute("SELECT id FROM users WHERE email = ?", (user_data.email,))
        if cursor.fetchone():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Generate user ID and verification token
        user_id = str(uuid.uuid4())
        verification_token = str(uuid.uuid4())
        
        # Hash password
        password_hash = hash_password(user_data.password)
        
        # Insert user into database
        cursor.execute('''
            INSERT INTO users (id, email, password_hash, verification_token)
            VALUES (?, ?, ?, ?)
        ''', (user_id, user_data.email, password_hash, verification_token))
        
        conn.commit()
        
        # Send verification email
        email_sent = send_verification_email(user_data.email, verification_token)
        
        return {
            "message": "User registered successfully",
            "email_sent": email_sent,
            "user_id": user_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}"
        )
    finally:
        conn.close()

@router.get("/verify-email")
async def verify_email(token: str):
    """
    Verify user email using verification token
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # Find user with verification token
        cursor.execute(
            "SELECT id, email FROM users WHERE verification_token = ? AND is_verified = FALSE",
            (token,)
        )
        user = cursor.fetchone()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired verification token"
            )
        
        # Update user as verified
        cursor.execute(
            "UPDATE users SET is_verified = TRUE, verification_token = NULL WHERE id = ?",
            (user['id'],)
        )
        conn.commit()
        
        return {"message": "Email verified successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Email verification failed: {str(e)}"
        )
    finally:
        conn.close()
