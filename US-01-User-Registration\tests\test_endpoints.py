"""
Test suite for US-01 User Registration endpoints
Tests all authentication functionality including registration, login, and email verification
"""

import requests
import json
import sqlite3
import os
import time
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8001"
TEST_DB_PATH = "../database/test_users.db"

class TestUserRegistration:
    """
    Test class for user registration functionality
    Tests the complete user registration flow
    """
    
    @classmethod
    def setup_class(cls):
        """Setup test environment before running tests"""
        print("Setting up test environment...")
        
        # Create test database
        cls.setup_test_database()
        
        # Test data
        cls.valid_user = {
            "email": "<EMAIL>",
            "password": "TestPass123!",
            "confirm_password": "TestPass123!"
        }
        
        cls.invalid_user = {
            "email": "invalid-email",
            "password": "weak",
            "confirm_password": "different"
        }
    
    @classmethod
    def setup_test_database(cls):
        """Create a clean test database"""
        if os.path.exists(TEST_DB_PATH):
            os.remove(TEST_DB_PATH)
        
        conn = sqlite3.connect(TEST_DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE users (
                id TEXT PRIMARY KEY,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                is_verified BOOLEAN DEFAULT FALSE,
                verification_token TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def test_health_check(self):
        """
        Test the health check endpoint
        Purpose: Verify the service is running and responding
        """
        response = requests.get(f"{BASE_URL}/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "US-01 User Registration"
        print("✓ Health check endpoint working")
    
    def test_register_valid_user(self):
        """
        Test user registration with valid data
        Purpose: Verify successful user registration flow
        """
        response = requests.post(
            f"{BASE_URL}/register",
            json=self.valid_user
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "user_id" in data
        assert data["message"] == "User registered successfully"
        print("✓ Valid user registration working")
    
    def test_register_duplicate_email(self):
        """
        Test registration with already existing email
        Purpose: Verify duplicate email prevention
        """
        # Try to register the same user again
        response = requests.post(
            f"{BASE_URL}/register",
            json=self.valid_user
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "Email already registered" in data["detail"]
        print("✓ Duplicate email prevention working")
    
    def test_register_invalid_email(self):
        """
        Test registration with invalid email format
        Purpose: Verify email validation
        """
        invalid_data = self.valid_user.copy()
        invalid_data["email"] = "invalid-email"
        
        response = requests.post(
            f"{BASE_URL}/register",
            json=invalid_data
        )
        
        assert response.status_code == 422  # Validation error
        print("✓ Email validation working")
    
    def test_register_weak_password(self):
        """
        Test registration with weak password
        Purpose: Verify password strength validation
        """
        weak_password_data = self.valid_user.copy()
        weak_password_data["password"] = "weak"
        weak_password_data["confirm_password"] = "weak"
        
        response = requests.post(
            f"{BASE_URL}/register",
            json=weak_password_data
        )
        
        assert response.status_code == 422  # Validation error
        print("✓ Password strength validation working")
    
    def test_register_password_mismatch(self):
        """
        Test registration with mismatched passwords
        Purpose: Verify password confirmation validation
        """
        mismatch_data = self.valid_user.copy()
        mismatch_data["confirm_password"] = "DifferentPass123!"
        
        response = requests.post(
            f"{BASE_URL}/register",
            json=mismatch_data
        )
        
        assert response.status_code == 422  # Validation error
        print("✓ Password mismatch validation working")
    
    def test_login_unverified_user(self):
        """
        Test login with unverified email
        Purpose: Verify email verification requirement
        """
        login_data = {
            "email": self.valid_user["email"],
            "password": self.valid_user["password"]
        }
        
        response = requests.post(
            f"{BASE_URL}/login",
            json=login_data
        )
        
        assert response.status_code == 401
        data = response.json()
        assert "Email not verified" in data["detail"]
        print("✓ Email verification requirement working")
    
    def test_verify_email_invalid_token(self):
        """
        Test email verification with invalid token
        Purpose: Verify token validation
        """
        response = requests.get(
            f"{BASE_URL}/verify-email?token=invalid-token"
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "Invalid or expired verification token" in data["detail"]
        print("✓ Invalid token handling working")
    
    def test_login_invalid_credentials(self):
        """
        Test login with wrong password
        Purpose: Verify credential validation
        """
        login_data = {
            "email": self.valid_user["email"],
            "password": "WrongPassword123!"
        }
        
        response = requests.post(
            f"{BASE_URL}/login",
            json=login_data
        )
        
        assert response.status_code == 401
        data = response.json()
        assert "Invalid email or password" in data["detail"]
        print("✓ Invalid credentials handling working")
    
    def test_login_nonexistent_user(self):
        """
        Test login with non-existent email
        Purpose: Verify user existence check
        """
        login_data = {
            "email": "<EMAIL>",
            "password": "SomePassword123!"
        }
        
        response = requests.post(
            f"{BASE_URL}/login",
            json=login_data
        )
        
        assert response.status_code == 401
        data = response.json()
        assert "Invalid email or password" in data["detail"]
        print("✓ Non-existent user handling working")

def run_manual_tests():
    """
    Manual testing instructions for endpoints
    Run this to get step-by-step testing guide
    """
    
    print("\n" + "="*60)
    print("MANUAL TESTING GUIDE FOR US-01 USER REGISTRATION")
    print("="*60)
    
    print("\n1. START THE SERVER:")
    print("   cd US-01-User-Registration/backend")
    print("   python app.py")
    print("   Server should start on http://localhost:8001")
    
    print("\n2. TEST HEALTH CHECK:")
    print("   curl http://localhost:8001/health")
    print("   Expected: {'status': 'healthy', 'service': 'US-01 User Registration'}")
    
    print("\n3. TEST USER REGISTRATION:")
    print("   curl -X POST http://localhost:8001/register \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -d '{")
    print("              \"email\": \"<EMAIL>\",")
    print("              \"password\": \"TestPass123!\",")
    print("              \"confirm_password\": \"TestPass123!\"")
    print("            }'")
    print("   Expected: Registration success message with user_id")
    
    print("\n4. TEST LOGIN (UNVERIFIED):")
    print("   curl -X POST http://localhost:8001/login \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -d '{")
    print("              \"email\": \"<EMAIL>\",")
    print("              \"password\": \"TestPass123!\"")
    print("            }'")
    print("   Expected: 401 error - Email not verified")
    
    print("\n5. VERIFY EMAIL (MANUAL):")
    print("   - Check database for verification_token")
    print("   - Visit: http://localhost:8001/verify-email?token=<token>")
    print("   - Expected: Email verified successfully")
    
    print("\n6. TEST LOGIN (VERIFIED):")
    print("   curl -X POST http://localhost:8001/login \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -d '{")
    print("              \"email\": \"<EMAIL>\",")
    print("              \"password\": \"TestPass123!\"")
    print("            }'")
    print("   Expected: JWT token in response")
    
    print("\n7. TEST API DOCUMENTATION:")
    print("   Visit: http://localhost:8001/docs")
    print("   Expected: Interactive API documentation")
    
    print("\n8. TEST ERROR CASES:")
    print("   - Invalid email format")
    print("   - Weak password")
    print("   - Password mismatch")
    print("   - Duplicate email registration")
    print("   - Wrong login credentials")
    
    print("\n" + "="*60)
    print("TESTING CHECKLIST:")
    print("="*60)
    print("□ Health check responds correctly")
    print("□ User registration works with valid data")
    print("□ Email validation prevents invalid emails")
    print("□ Password validation enforces strength requirements")
    print("□ Password confirmation prevents mismatches")
    print("□ Duplicate email registration is prevented")
    print("□ Login fails for unverified users")
    print("□ Email verification works with valid token")
    print("□ Login succeeds for verified users")
    print("□ JWT token is returned on successful login")
    print("□ Invalid credentials are rejected")
    print("□ API documentation is accessible")

if __name__ == "__main__":
    """
    Run tests when script is executed directly
    """
    
    print("US-01 User Registration - Endpoint Testing")
    print("=" * 50)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✓ Server is running, starting automated tests...")
            
            # Run automated tests
            test_suite = TestUserRegistration()
            test_suite.setup_class()
            
            # Run individual tests
            test_methods = [
                test_suite.test_health_check,
                test_suite.test_register_valid_user,
                test_suite.test_register_duplicate_email,
                test_suite.test_register_invalid_email,
                test_suite.test_register_weak_password,
                test_suite.test_register_password_mismatch,
                test_suite.test_login_unverified_user,
                test_suite.test_verify_email_invalid_token,
                test_suite.test_login_invalid_credentials,
                test_suite.test_login_nonexistent_user
            ]
            
            passed = 0
            failed = 0
            
            for test_method in test_methods:
                try:
                    test_method()
                    passed += 1
                except Exception as e:
                    print(f"✗ {test_method.__name__} failed: {e}")
                    failed += 1
            
            print(f"\nTest Results: {passed} passed, {failed} failed")
            
        else:
            print("✗ Server is not responding correctly")
            
    except requests.exceptions.ConnectionError:
        print("✗ Cannot connect to server. Please start the server first:")
        print("  cd US-01-User-Registration/backend")
        print("  python app.py")
    
    # Show manual testing guide
    run_manual_tests()
