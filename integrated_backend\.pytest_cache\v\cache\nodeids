["tests/test_integrated_backend.py::TestIntegratedBackend::test_01_health_check", "tests/test_integrated_backend.py::TestIntegratedBackend::test_02_root_endpoint", "tests/test_integrated_backend.py::TestIntegratedBackend::test_03_user_registration", "tests/test_integrated_backend.py::TestIntegratedBackend::test_04_duplicate_registration", "tests/test_integrated_backend.py::TestIntegratedBackend::test_05_invalid_password", "tests/test_integrated_backend.py::TestIntegratedBackend::test_06_login_unverified", "tests/test_integrated_backend.py::TestIntegratedBackend::test_07_manual_email_verification", "tests/test_integrated_backend.py::TestIntegratedBackend::test_08_successful_login", "tests/test_integrated_backend.py::TestIntegratedBackend::test_09_get_user_profile", "tests/test_integrated_backend.py::TestIntegratedBackend::test_10_invalid_credentials", "tests/test_integrated_backend.py::TestIntegratedBackend::test_11_resume_upload_pdf", "tests/test_integrated_backend.py::TestIntegratedBackend::test_12_resume_list", "tests/test_integrated_backend.py::TestIntegratedBackend::test_13_resume_get", "tests/test_integrated_backend.py::TestIntegratedBackend::test_14_invalid_file_upload", "tests/test_integrated_backend.py::TestIntegratedBackend::test_15_job_description_text_upload", "tests/test_integrated_backend.py::TestIntegratedBackend::test_16_job_description_list", "tests/test_integrated_backend.py::TestIntegratedBackend::test_17_job_description_get", "tests/test_integrated_backend.py::TestIntegratedBackend::test_18_empty_job_description", "tests/test_integrated_backend.py::TestIntegratedBackend::test_19_unauthorized_access", "tests/test_integrated_backend.py::TestIntegratedBackend::test_20_keyword_extraction_from_resume", "tests/test_integrated_backend.py::TestIntegratedBackend::test_20_logout"]