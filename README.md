# OpenResume AI - Microservice Architecture

## Project Overview
OpenResume AI is a comprehensive resume building and optimization platform built using microservice architecture. Each user story is implemented as an independent microservice with its own backend, database, frontend components, documentation, and tests.

## Technology Stack

### Backend
- **Framework**: FastAPI (Python)
- **Database**: SQLite (development), PostgreSQL (production)
- **Authentication**: JWT tokens with bcrypt password hashing
- **Email**: SMTP integration for verification emails
- **API Documentation**: Automatic OpenAPI/Swagger documentation

### Frontend
- **Framework**: React.js with TypeScript
- **Styling**: Tailwind CSS
- **HTTP Client**: Axios
- **State Management**: React Hooks
- **Routing**: React Router

### Development Tools
- **Testing**: pytest for backend, Jest for frontend
- **Code Quality**: ESLint, Prettier, <PERSON> (Python formatter)
- **Documentation**: Markdown with line-by-line code explanations
- **Containerization**: <PERSON><PERSON> and <PERSON>er Compose

## Microservice Architecture

Each user story follows this standardized folder structure:

```
US-XX-Feature-Name/
├── backend/           # FastAPI application and business logic
├── database/          # Database schema, migrations, and utilities
├── docs/             # Detailed documentation with line-by-line explanations
├── frontend/         # React components and pages
└── tests/            # Automated tests and testing documentation
```

## Current Microservices

### US-01: User Registration & Authentication
**Status**: ✅ Implemented
**Endpoint**: `http://localhost:8001`
**Features**:
- User registration with email/password
- Email verification system
- Secure login with JWT tokens
- Password strength validation
- Duplicate email prevention

**API Endpoints**:
- `POST /register` - Register new user
- `POST /login` - Authenticate user
- `GET /verify-email` - Verify email address
- `GET /health` - Health check

### Planned Microservices

- **US-02**: Login with JWT Token Management
- **US-03**: Resume Upload and Parsing
- **US-04**: Job Description Upload
- **US-05**: Keyword Parsing and Analysis
- **US-06**: Resume-Job Matching Score
- **US-07**: AI-Powered Suggestions
- **US-08**: User Dashboard
- **US-09**: API Rate Limiting and Protection
- **US-10**: Account Settings and Profile Management

## Getting Started

### Prerequisites
- Python 3.8+
- Node.js 16+
- npm or yarn
- Git

### Quick Start

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd openresume-ai
   ```

2. **Start US-01 User Registration Service**:
   ```bash
   # Backend
   cd US-01-User-Registration/backend
   pip install -r requirements.txt
   python app.py
   
   # Database (in new terminal)
   cd US-01-User-Registration/database
   python init_db.py
   
   # Frontend (in new terminal)
   cd US-01-User-Registration/frontend
   npm install
   npm start
   ```

3. **Access the services**:
   - Backend API: `http://localhost:8001`
   - API Documentation: `http://localhost:8001/docs`
   - Frontend: `http://localhost:3000`

### Environment Configuration

Create `.env` files in each microservice backend folder:

```env
# US-01-User-Registration/backend/.env
SECRET_KEY=your-super-secret-jwt-key-change-in-production
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your-app-password
```

## Development Workflow

### Adding a New Microservice

1. **Create folder structure**:
   ```bash
   mkdir US-XX-Feature-Name
   cd US-XX-Feature-Name
   mkdir backend database docs frontend tests
   ```

2. **Backend setup**:
   - Create FastAPI application in `backend/app.py`
   - Define Pydantic models for request/response validation
   - Implement business logic and database operations
   - Add comprehensive error handling

3. **Database setup**:
   - Create database schema in `database/init_db.py`
   - Add migration scripts if needed
   - Include database utilities and helpers

4. **Frontend setup**:
   - Create React components in `frontend/components/`
   - Implement forms with validation
   - Add API integration with error handling
   - Style with Tailwind CSS

5. **Documentation**:
   - Write detailed API documentation in `docs/README.md`
   - Include line-by-line code explanations
   - Document integration points and usage examples

6. **Testing**:
   - Create automated tests in `tests/test_endpoints.py`
   - Write testing documentation in `tests/README.md`
   - Include manual testing procedures

### Code Standards

- **Python**: Follow PEP 8, use type hints, comprehensive docstrings
- **JavaScript/React**: Use TypeScript, functional components with hooks
- **API Design**: RESTful endpoints, consistent error responses
- **Security**: Input validation, SQL injection prevention, secure password handling
- **Documentation**: Line-by-line explanations for complex logic

## Testing

### Automated Testing
Each microservice includes automated tests:

```bash
# Backend tests
cd US-01-User-Registration/tests
python test_endpoints.py

# Frontend tests (when implemented)
cd US-01-User-Registration/frontend
npm test
```

### Manual Testing
Follow the testing procedures in each microservice's `tests/README.md`:

1. Start the backend service
2. Initialize the database
3. Test each endpoint with curl or Postman
4. Verify frontend functionality
5. Test error scenarios

## Deployment

### Development
Each microservice runs independently on different ports:
- US-01: `http://localhost:8001`
- US-02: `http://localhost:8002`
- US-03: `http://localhost:8003`
- etc.

### Production
Use Docker Compose for orchestrated deployment:

```bash
docker-compose up -d
```

## API Documentation

Each microservice provides interactive API documentation:
- US-01: `http://localhost:8001/docs`

## Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/US-XX-feature-name`
3. **Follow the microservice structure** outlined above
4. **Write comprehensive tests** and documentation
5. **Submit a pull request** with detailed description

## Security Considerations

- **Authentication**: JWT tokens with secure secret keys
- **Password Security**: bcrypt hashing with salt
- **Input Validation**: Pydantic models prevent malicious input
- **SQL Injection**: Parameterized queries only
- **CORS**: Properly configured for production
- **Rate Limiting**: Implemented per microservice
- **Email Verification**: Required for account activation

## Monitoring and Logging

- **Health Checks**: Each service provides `/health` endpoint
- **Logging**: Structured logging for debugging and monitoring
- **Error Tracking**: Comprehensive error handling and reporting
- **Performance**: Response time monitoring for each endpoint

## Support

For questions or issues:
1. Check the microservice-specific documentation in `docs/README.md`
2. Review the testing procedures in `tests/README.md`
3. Examine the automated tests for usage examples
4. Create an issue with detailed description and steps to reproduce

## License

This project is licensed under the MIT License - see the LICENSE file for details.
