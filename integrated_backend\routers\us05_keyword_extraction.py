"""
US-05: Keyword Extraction & Parsing Router
Handles keyword extraction from uploaded files and text parsing
"""

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import List, Dict, Optional, Any
import uuid
from datetime import datetime

from database.database import get_db_connection
from routers.us02_login import get_current_user, CurrentUser
from utils.nlp_utils import comprehensive_keyword_extraction

router = APIRouter()

# Pydantic models
class KeywordExtractionRequest(BaseModel):
    document_id: str
    document_type: str  # 'resume' or 'job_description'

class TextAnalysisRequest(BaseModel):
    text: str
    analysis_type: Optional[str] = "comprehensive"  # 'comprehensive', 'keywords_only', 'skills_only'

class KeywordExtractionResponse(BaseModel):
    analysis_id: str
    document_id: str
    document_type: str
    tfidf_keywords: List[tuple]
    technical_skills: Dict[str, List[str]]
    job_roles: List[str]
    soft_skills: List[str]
    entities: Dict[str, List[str]]
    education: Dict[str, List[str]]
    experience_years: Optional[int]
    text_stats: Dict[str, int]
    created_at: datetime

class TextAnalysisResponse(BaseModel):
    analysis_id: str
    tfidf_keywords: List[tuple]
    technical_skills: Dict[str, List[str]]
    job_roles: List[str]
    soft_skills: List[str]
    entities: Dict[str, List[str]]
    education: Dict[str, List[str]]
    experience_years: Optional[int]
    text_stats: Dict[str, int]
    created_at: datetime

class AnalysisListResponse(BaseModel):
    id: str
    document_id: Optional[str]
    document_type: Optional[str]
    analysis_type: str
    created_at: datetime
    summary: Dict[str, Any]

# Utility functions
def get_document_text(document_id: str, document_type: str, user_id: str) -> str:
    """Retrieve document text from database"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        if document_type == 'resume':
            cursor.execute('''
                SELECT processed_text FROM resume_uploads 
                WHERE id = ? AND user_id = ?
            ''', (document_id, user_id))
        elif document_type == 'job_description':
            cursor.execute('''
                SELECT processed_text FROM job_descriptions 
                WHERE id = ? AND user_id = ?
            ''', (document_id, user_id))
        else:
            raise ValueError(f"Invalid document type: {document_type}")
        
        result = cursor.fetchone()
        if not result:
            raise ValueError(f"Document not found: {document_id}")
        
        return result['processed_text'] or ""
        
    finally:
        conn.close()

def save_analysis_results(analysis_id: str, user_id: str, document_id: Optional[str], 
                         document_type: Optional[str], analysis_type: str, 
                         results: Dict[str, Any]) -> None:
    """Save analysis results to database"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            INSERT INTO analysis_results (
                id, user_id, resume_id, job_description_id, analysis_type, results_json
            ) VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            analysis_id, user_id,
            document_id if document_type == 'resume' else None,
            document_id if document_type == 'job_description' else None,
            analysis_type,
            str(results)  # Convert to string for storage
        ))
        
        conn.commit()
        
    finally:
        conn.close()

# API Endpoints
@router.post("/extract-keywords", response_model=KeywordExtractionResponse)
async def extract_keywords_from_document(
    request: KeywordExtractionRequest,
    current_user: CurrentUser = Depends(get_current_user)
):
    """
    Extract keywords and entities from uploaded documents
    - Supports both resume and job description documents
    - Extracts technical skills, soft skills, job roles, and entities
    - Returns comprehensive analysis results
    """
    
    try:
        # Validate document type
        if request.document_type not in ['resume', 'job_description']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Document type must be 'resume' or 'job_description'"
            )
        
        # Get document text
        try:
            document_text = get_document_text(
                request.document_id, 
                request.document_type, 
                current_user.id
            )
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        
        if not document_text.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Document contains no extractable text"
            )
        
        # Perform keyword extraction and analysis
        analysis_results = comprehensive_keyword_extraction(document_text)
        
        # Generate analysis ID
        analysis_id = str(uuid.uuid4())
        
        # Save results to database
        save_analysis_results(
            analysis_id=analysis_id,
            user_id=current_user.id,
            document_id=request.document_id,
            document_type=request.document_type,
            analysis_type="keyword_extraction",
            results=analysis_results
        )
        
        return KeywordExtractionResponse(
            analysis_id=analysis_id,
            document_id=request.document_id,
            document_type=request.document_type,
            tfidf_keywords=analysis_results.get('tfidf_keywords', []),
            technical_skills=analysis_results.get('technical_skills', {}),
            job_roles=analysis_results.get('job_roles', []),
            soft_skills=analysis_results.get('soft_skills', []),
            entities=analysis_results.get('entities', {}),
            education=analysis_results.get('education', {}),
            experience_years=analysis_results.get('experience_years'),
            text_stats=analysis_results.get('text_stats', {}),
            created_at=datetime.now()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Keyword extraction failed: {str(e)}"
        )

@router.post("/analyze-text", response_model=TextAnalysisResponse)
async def analyze_text_content(
    request: TextAnalysisRequest,
    current_user: CurrentUser = Depends(get_current_user)
):
    """
    Analyze raw text content for keywords and entities
    - Accepts plain text input
    - Performs comprehensive NLP analysis
    - Returns extracted keywords, skills, and entities
    """
    
    try:
        if not request.text.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Text content cannot be empty"
            )
        
        # Perform analysis based on type
        if request.analysis_type == "comprehensive":
            analysis_results = comprehensive_keyword_extraction(request.text)
        else:
            # For now, always do comprehensive analysis
            analysis_results = comprehensive_keyword_extraction(request.text)
        
        # Generate analysis ID
        analysis_id = str(uuid.uuid4())
        
        # Save results to database
        save_analysis_results(
            analysis_id=analysis_id,
            user_id=current_user.id,
            document_id=None,
            document_type=None,
            analysis_type=f"text_analysis_{request.analysis_type}",
            results=analysis_results
        )
        
        return TextAnalysisResponse(
            analysis_id=analysis_id,
            tfidf_keywords=analysis_results.get('tfidf_keywords', []),
            technical_skills=analysis_results.get('technical_skills', {}),
            job_roles=analysis_results.get('job_roles', []),
            soft_skills=analysis_results.get('soft_skills', []),
            entities=analysis_results.get('entities', {}),
            education=analysis_results.get('education', {}),
            experience_years=analysis_results.get('experience_years'),
            text_stats=analysis_results.get('text_stats', {}),
            created_at=datetime.now()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Text analysis failed: {str(e)}"
        )

@router.get("/analyses", response_model=List[AnalysisListResponse])
async def list_analyses(current_user: CurrentUser = Depends(get_current_user)):
    """
    List all keyword extraction analyses for the current user
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT id, resume_id, job_description_id, analysis_type, created_at, results_json
            FROM analysis_results 
            WHERE user_id = ? AND analysis_type LIKE '%keyword%' OR analysis_type LIKE '%text_analysis%'
            ORDER BY created_at DESC
        ''', (current_user.id,))
        
        analyses = cursor.fetchall()
        
        result = []
        for analysis in analyses:
            # Determine document info
            document_id = analysis['resume_id'] or analysis['job_description_id']
            document_type = 'resume' if analysis['resume_id'] else ('job_description' if analysis['job_description_id'] else None)
            
            # Create summary from results
            summary = {
                'has_technical_skills': 'technical_skills' in analysis['results_json'],
                'has_job_roles': 'job_roles' in analysis['results_json'],
                'has_entities': 'entities' in analysis['results_json']
            }
            
            result.append(AnalysisListResponse(
                id=analysis['id'],
                document_id=document_id,
                document_type=document_type,
                analysis_type=analysis['analysis_type'],
                created_at=datetime.fromisoformat(analysis['created_at']),
                summary=summary
            ))
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list analyses: {str(e)}"
        )
    finally:
        conn.close()

@router.get("/analysis/{analysis_id}")
async def get_analysis_details(
    analysis_id: str,
    current_user: CurrentUser = Depends(get_current_user)
):
    """
    Get detailed results of a specific analysis
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT * FROM analysis_results 
            WHERE id = ? AND user_id = ?
        ''', (analysis_id, current_user.id))
        
        analysis = cursor.fetchone()
        
        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis not found"
            )
        
        return {
            "id": analysis['id'],
            "document_id": analysis['resume_id'] or analysis['job_description_id'],
            "document_type": 'resume' if analysis['resume_id'] else ('job_description' if analysis['job_description_id'] else None),
            "analysis_type": analysis['analysis_type'],
            "results": analysis['results_json'],
            "created_at": analysis['created_at']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get analysis details: {str(e)}"
        )
    finally:
        conn.close()
