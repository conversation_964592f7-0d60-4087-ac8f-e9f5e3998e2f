#!/usr/bin/env python3
"""
Debug script to test the new endpoints
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_new_endpoints():
    """Test the new endpoints with proper authentication"""
    
    # Step 1: Register a test user
    print("1. Registering test user...")
    user_data = {
        "email": "<EMAIL>",
        "password": "TestPass123!",
        "confirm_password": "TestPass123!"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/register", json=user_data)
    print(f"Registration: {response.status_code}")
    if response.status_code != 200:
        print(f"Registration failed: {response.text}")
        return
    
    user_id = response.json()["user_id"]
    print(f"User ID: {user_id}")
    
    # Step 2: Manually verify email (since we don't have email setup)
    print("2. Manually verifying email...")
    verify_response = requests.post(f"{BASE_URL}/api/v1/auth/verify-email", json={"user_id": user_id})
    print(f"Verification: {verify_response.status_code}")
    
    # Step 3: Login
    print("3. Logging in...")
    login_data = {
        "email": "<EMAIL>",
        "password": "TestPass123!"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/login", json=login_data)
    print(f"Login: {response.status_code}")
    if response.status_code != 200:
        print(f"Login failed: {response.text}")
        return
    
    access_token = response.json()["access_token"]
    headers = {"Authorization": f"Bearer {access_token}"}
    print("✓ Authentication successful")
    
    # Step 4: Upload a test resume
    print("4. Uploading test resume...")
    resume_text = """
    John Doe
    Software Engineer
    
    Experience:
    - 5 years of Python development
    - Worked with Django, Flask, FastAPI
    - Experience with PostgreSQL, Redis
    - AWS cloud deployment
    
    Skills:
    - Python, JavaScript, SQL
    - React, Node.js
    - Docker, Kubernetes
    - Git, Jenkins
    """
    
    job_data = {
        "title": "Test Resume",
        "company": "Test Company",
        "description_text": resume_text
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/job/upload-text", json=job_data, headers=headers)
    print(f"Resume upload: {response.status_code}")
    if response.status_code != 200:
        print(f"Resume upload failed: {response.text}")
        return
    
    resume_id = response.json()["job_id"]  # Using job endpoint as proxy for resume
    print(f"Resume ID: {resume_id}")
    
    # Step 5: Test keyword extraction
    print("5. Testing keyword extraction...")
    payload = {
        "document_id": resume_id,
        "document_type": "job_description"  # Using job_description since we uploaded via job endpoint
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/keywords/extract-keywords", json=payload, headers=headers)
    print(f"Keyword extraction: {response.status_code}")
    if response.status_code != 200:
        print(f"Keyword extraction failed: {response.text}")
        return
    
    analysis_data = response.json()
    print(f"✓ Keyword extraction successful!")
    print(f"Analysis ID: {analysis_data['analysis_id']}")
    print(f"Technical skills found: {len(analysis_data.get('technical_skills', {}))}")
    print(f"Keywords found: {len(analysis_data.get('tfidf_keywords', []))}")
    
    # Step 6: Test text analysis
    print("6. Testing direct text analysis...")
    text_payload = {
        "text": "I am a Python developer with 5 years of experience in Django, Flask, and FastAPI. I have worked with PostgreSQL, Redis, and AWS.",
        "analysis_type": "comprehensive"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/keywords/analyze-text", json=text_payload, headers=headers)
    print(f"Text analysis: {response.status_code}")
    if response.status_code != 200:
        print(f"Text analysis failed: {response.text}")
        return
    
    text_analysis = response.json()
    print(f"✓ Text analysis successful!")
    print(f"Experience years detected: {text_analysis.get('experience_years')}")
    
    # Step 7: Upload a job description
    print("7. Uploading job description...")
    job_desc = """
    Senior Python Developer
    
    Requirements:
    - 3+ years Python experience
    - Django or Flask framework
    - PostgreSQL database
    - AWS cloud experience
    - Docker containerization
    - Git version control
    
    Nice to have:
    - FastAPI experience
    - Redis caching
    - Kubernetes
    """
    
    job_data = {
        "title": "Senior Python Developer",
        "company": "Tech Corp",
        "description_text": job_desc
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/job/upload-text", json=job_data, headers=headers)
    print(f"Job description upload: {response.status_code}")
    if response.status_code != 200:
        print(f"Job description upload failed: {response.text}")
        return
    
    job_id = response.json()["job_id"]
    print(f"Job ID: {job_id}")
    
    # Step 8: Test matching score
    print("8. Testing matching score calculation...")
    match_payload = {
        "resume_id": resume_id,
        "job_description_id": job_id
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/matching/calculate-match", json=match_payload, headers=headers)
    print(f"Matching score: {response.status_code}")
    if response.status_code != 200:
        print(f"Matching score failed: {response.text}")
        return
    
    match_data = response.json()
    print(f"✓ Matching score calculated!")
    print(f"Overall score: {match_data['overall_score']}%")
    print(f"Match ID: {match_data['match_id']}")
    
    # Step 9: Test suggestions
    print("9. Testing suggestions generation...")
    suggestion_payload = {
        "match_id": match_data['match_id']
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/suggestions/generate", json=suggestion_payload, headers=headers)
    print(f"Suggestions: {response.status_code}")
    if response.status_code != 200:
        print(f"Suggestions failed: {response.text}")
        return
    
    suggestions_data = response.json()
    print(f"✓ Suggestions generated!")
    print(f"Number of suggestions: {len(suggestions_data['suggestions'])}")
    print(f"Quick wins: {len(suggestions_data['quick_wins'])}")
    
    print("\n🎉 All new endpoints working successfully!")

if __name__ == "__main__":
    test_new_endpoints()
