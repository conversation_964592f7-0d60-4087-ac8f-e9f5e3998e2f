"""
Comprehensive Test Suite for OpenResume AI Integrated Backend
Tests all user stories: US-01, US-02, US-03, US-04
"""

import requests
import json
import os
import tempfile
from datetime import datetime
import pytest

# Test configuration
BASE_URL = "http://localhost:8000"

class TestIntegratedBackend:
    """
    Test class for the integrated backend covering all user stories
    """
    
    @classmethod
    def setup_class(cls):
        """Setup test environment"""
        print("Setting up integrated backend tests...")
        
        # Test user data
        cls.test_user = {
            "email": "<EMAIL>",
            "password": "TestPass123!",
            "confirm_password": "TestPass123!"
        }
        
        cls.test_login = {
            "email": "<EMAIL>",
            "password": "TestPass123!"
        }
        
        cls.access_token = None
        cls.user_id = None
        cls.resume_id = None
        cls.job_id = None
    
    def test_01_health_check(self):
        """Test the main health check endpoint"""
        response = requests.get(f"{BASE_URL}/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "OpenResume AI" in data["service"]
        print("✓ Health check working")
    
    def test_02_root_endpoint(self):
        """Test the root endpoint"""
        response = requests.get(f"{BASE_URL}/")
        
        assert response.status_code == 200
        data = response.json()
        assert "OpenResume AI" in data["message"]
        assert "services" in data
        assert len(data["services"]) == 4
        print("✓ Root endpoint working")
    
    # US-01: User Registration Tests
    def test_03_user_registration(self):
        """Test user registration (US-01)"""
        response = requests.post(
            f"{BASE_URL}/api/v1/auth/register",
            json=self.test_user
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "user_id" in data
        self.__class__.user_id = data["user_id"]
        print("✓ User registration working")
    
    def test_04_duplicate_registration(self):
        """Test duplicate email registration prevention"""
        response = requests.post(
            f"{BASE_URL}/api/v1/auth/register",
            json=self.test_user
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "Email already registered" in data["detail"]
        print("✓ Duplicate registration prevention working")
    
    def test_05_invalid_password(self):
        """Test password validation"""
        invalid_user = self.test_user.copy()
        invalid_user["email"] = "<EMAIL>"
        invalid_user["password"] = "weak"
        invalid_user["confirm_password"] = "weak"
        
        response = requests.post(
            f"{BASE_URL}/api/v1/auth/register",
            json=invalid_user
        )
        
        assert response.status_code == 422
        print("✓ Password validation working")
    
    # US-02: Authentication Tests
    def test_06_login_unverified(self):
        """Test login with unverified email"""
        response = requests.post(
            f"{BASE_URL}/api/v1/auth/login",
            json=self.test_login
        )
        
        assert response.status_code == 401
        data = response.json()
        assert "Email not verified" in data["detail"]
        print("✓ Unverified email login prevention working")
    
    def test_07_manual_email_verification(self):
        """Manually verify email for testing"""
        # This would normally be done via email link
        # For testing, we'll directly update the database
        from database.database import get_db_connection
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "UPDATE users SET is_verified = TRUE WHERE email = ?",
                (self.test_user["email"],)
            )
            conn.commit()
            print("✓ Email manually verified for testing")
        finally:
            conn.close()
    
    def test_08_successful_login(self):
        """Test successful login after verification"""
        response = requests.post(
            f"{BASE_URL}/api/v1/auth/login",
            json=self.test_login
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        self.__class__.access_token = data["access_token"]
        print("✓ Successful login working")
    
    def test_09_get_user_profile(self):
        """Test getting user profile"""
        headers = {"Authorization": f"Bearer {self.access_token}"}
        response = requests.get(f"{BASE_URL}/api/v1/auth/me", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == self.test_user["email"]
        assert data["is_verified"] == True
        print("✓ User profile retrieval working")
    
    def test_10_invalid_credentials(self):
        """Test login with invalid credentials"""
        invalid_login = {
            "email": self.test_user["email"],
            "password": "WrongPassword123!"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/auth/login",
            json=invalid_login
        )
        
        assert response.status_code == 401
        print("✓ Invalid credentials rejection working")
    
    # US-03: Resume Upload Tests
    def test_11_resume_upload_pdf(self):
        """Test PDF resume upload"""
        # Create a dummy PDF file for testing
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp_file:
            tmp_file.write(b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n")
            tmp_file_path = tmp_file.name
        
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            
            with open(tmp_file_path, 'rb') as f:
                files = {"file": ("test_resume.pdf", f, "application/pdf")}
                response = requests.post(
                    f"{BASE_URL}/api/v1/resume/upload",
                    headers=headers,
                    files=files
                )
            
            assert response.status_code == 200
            data = response.json()
            assert "document_id" in data
            assert data["upload_status"] == "uploaded"
            self.__class__.resume_id = data["document_id"]
            print("✓ PDF resume upload working")
            
        finally:
            os.unlink(tmp_file_path)
    
    def test_12_resume_list(self):
        """Test listing uploaded resumes"""
        headers = {"Authorization": f"Bearer {self.access_token}"}
        response = requests.get(f"{BASE_URL}/api/v1/resume/list", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) > 0
        print("✓ Resume listing working")
    
    def test_13_resume_get(self):
        """Test getting specific resume"""
        headers = {"Authorization": f"Bearer {self.access_token}"}
        response = requests.get(
            f"{BASE_URL}/api/v1/resume/{self.resume_id}",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == self.resume_id
        print("✓ Resume retrieval working")
    
    def test_14_invalid_file_upload(self):
        """Test upload with invalid file type"""
        with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as tmp_file:
            tmp_file.write(b"This is not a valid resume file")
            tmp_file_path = tmp_file.name
        
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            
            with open(tmp_file_path, 'rb') as f:
                files = {"file": ("test.txt", f, "text/plain")}
                response = requests.post(
                    f"{BASE_URL}/api/v1/resume/upload",
                    headers=headers,
                    files=files
                )
            
            assert response.status_code == 400
            print("✓ Invalid file type rejection working")
            
        finally:
            os.unlink(tmp_file_path)
    
    # US-04: Job Description Upload Tests
    def test_15_job_description_text_upload(self):
        """Test job description text upload"""
        headers = {"Authorization": f"Bearer {self.access_token}"}
        job_data = {
            "title": "Software Engineer",
            "company": "Tech Corp",
            "description_text": "We are looking for a skilled software engineer..."
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/job/upload-text",
            headers=headers,
            json=job_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "job_id" in data
        assert data["upload_status"] == "uploaded"
        self.__class__.job_id = data["job_id"]
        print("✓ Job description text upload working")
    
    def test_16_job_description_list(self):
        """Test listing job descriptions"""
        headers = {"Authorization": f"Bearer {self.access_token}"}
        response = requests.get(f"{BASE_URL}/api/v1/job/list", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) > 0
        print("✓ Job description listing working")
    
    def test_17_job_description_get(self):
        """Test getting specific job description"""
        headers = {"Authorization": f"Bearer {self.access_token}"}
        response = requests.get(
            f"{BASE_URL}/api/v1/job/{self.job_id}",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == self.job_id
        print("✓ Job description retrieval working")
    
    def test_18_empty_job_description(self):
        """Test empty job description rejection"""
        headers = {"Authorization": f"Bearer {self.access_token}"}
        job_data = {
            "description_text": ""
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/job/upload-text",
            headers=headers,
            json=job_data
        )
        
        assert response.status_code == 400
        print("✓ Empty job description rejection working")
    
    def test_19_unauthorized_access(self):
        """Test unauthorized access to protected endpoints"""
        # Test without token
        response = requests.get(f"{BASE_URL}/api/v1/auth/me")
        assert response.status_code == 401
        
        # Test with invalid token
        headers = {"Authorization": "Bearer invalid_token"}
        response = requests.get(f"{BASE_URL}/api/v1/auth/me", headers=headers)
        assert response.status_code == 401
        
        print("✓ Unauthorized access protection working")
    
    def test_20_logout(self):
        """Test user logout"""
        headers = {"Authorization": f"Bearer {self.access_token}"}
        response = requests.post(f"{BASE_URL}/api/v1/auth/logout", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "Successfully logged out" in data["message"]
        print("✓ User logout working")

def run_manual_tests():
    """Manual testing guide for the integrated backend"""
    print("\n" + "="*80)
    print("MANUAL TESTING GUIDE FOR OPENRESUME AI INTEGRATED BACKEND")
    print("="*80)
    
    print("\n1. START THE INTEGRATED SERVER:")
    print("   cd integrated_backend")
    print("   python main.py")
    print("   Server should start on http://localhost:8000")
    
    print("\n2. TEST API DOCUMENTATION:")
    print("   Visit: http://localhost:8000/docs")
    print("   Expected: Interactive API documentation with all endpoints")
    
    print("\n3. TEST ALL USER STORIES:")
    print("   US-01: User Registration - /api/v1/auth/register")
    print("   US-02: Authentication - /api/v1/auth/login")
    print("   US-03: Resume Upload - /api/v1/resume/upload")
    print("   US-04: Job Description - /api/v1/job/upload-text")
    
    print("\n4. TESTING CHECKLIST:")
    print("   □ Health check responds correctly")
    print("   □ User registration and email verification")
    print("   □ User login and JWT token generation")
    print("   □ Resume upload (PDF/DOCX) and text extraction")
    print("   □ Job description upload (text/DOCX)")
    print("   □ File listing and retrieval")
    print("   □ Authentication protection")
    print("   □ Error handling and validation")

if __name__ == "__main__":
    """Run tests when script is executed directly"""
    
    print("OpenResume AI Integrated Backend - Comprehensive Testing")
    print("=" * 60)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✓ Server is running, starting automated tests...")
            
            # Run automated tests
            test_suite = TestIntegratedBackend()
            test_suite.setup_class()
            
            # Get all test methods
            test_methods = [getattr(test_suite, method) for method in dir(test_suite) 
                          if method.startswith('test_') and callable(getattr(test_suite, method))]
            
            # Sort test methods by name to ensure proper order
            test_methods.sort(key=lambda x: x.__name__)
            
            passed = 0
            failed = 0
            
            for test_method in test_methods:
                try:
                    test_method()
                    passed += 1
                except Exception as e:
                    print(f"✗ {test_method.__name__} failed: {e}")
                    failed += 1
            
            print(f"\nTest Results: {passed} passed, {failed} failed")
            
        else:
            print("✗ Server is not responding correctly")
            
    except requests.exceptions.ConnectionError:
        print("✗ Cannot connect to server. Please start the server first:")
        print("  cd integrated_backend")
        print("  python main.py")
    
    # Show manual testing guide
    run_manual_tests()
