"""
US-04: Job Description Upload Router
Handles job description uploads (plain text and DOCX)
"""

from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form, status
from pydantic import BaseModel
import os
import uuid
from datetime import datetime
import aiofiles
from typing import List, Optional
from docx import Document

from database.database import get_db_connection
from routers.us02_login import get_current_user, CurrentUser

router = APIRouter()

# Configuration
UPLOAD_DIR = os.path.join(os.path.dirname(__file__), "..", "uploads", "job_descriptions")
MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB
ALLOWED_EXTENSIONS = {".docx", ".txt"}

# Ensure upload directory exists
os.makedirs(UPLOAD_DIR, exist_ok=True)

# Pydantic models
class JobDescriptionUploadResponse(BaseModel):
    job_id: str
    title: Optional[str]
    company: Optional[str]
    description_text: str
    file_type: str
    upload_status: str
    message: str

class JobDescriptionText(BaseModel):
    title: Optional[str] = None
    company: Optional[str] = None
    description_text: str

class JobDescriptionInfo(BaseModel):
    id: str
    title: Optional[str]
    company: Optional[str]
    description_text: str
    original_filename: Optional[str]
    file_type: str
    processed_text: str
    created_at: datetime

# Utility functions
def get_file_extension(filename: str) -> str:
    """Get file extension from filename"""
    return os.path.splitext(filename)[1].lower()

def is_allowed_file(filename: str) -> bool:
    """Check if file extension is allowed"""
    return get_file_extension(filename) in ALLOWED_EXTENSIONS

async def save_uploaded_file(upload_file: UploadFile, file_path: str):
    """Save uploaded file to disk"""
    async with aiofiles.open(file_path, 'wb') as f:
        content = await upload_file.read()
        await f.write(content)
    return len(content)

def extract_text_from_docx(file_path: str) -> str:
    """Extract text from DOCX file"""
    try:
        doc = Document(file_path)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text.strip()
    except Exception as e:
        print(f"Error extracting text from DOCX: {e}")
        return ""

def extract_text_from_txt(file_path: str) -> str:
    """Extract text from TXT file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read().strip()
    except Exception as e:
        print(f"Error reading text file: {e}")
        return ""

def process_job_description_text(file_path: str, file_type: str) -> str:
    """Process job description and extract text based on file type"""
    if file_type == ".docx":
        return extract_text_from_docx(file_path)
    elif file_type == ".txt":
        return extract_text_from_txt(file_path)
    else:
        return ""

def clean_job_description_text(text: str) -> str:
    """Clean and format job description text"""
    # Remove excessive whitespace and normalize line breaks
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    return '\n'.join(lines)

# API Endpoints
@router.post("/upload-file", response_model=JobDescriptionUploadResponse)
async def upload_job_description_file(
    file: UploadFile = File(...),
    title: Optional[str] = Form(None),
    company: Optional[str] = Form(None),
    current_user: CurrentUser = Depends(get_current_user)
):
    """
    Upload job description file (DOCX or TXT)
    - Validates file type and size
    - Saves file to disk
    - Extracts and processes text content
    - Returns job ID and processed text
    """
    
    # Validate file
    if not file.filename:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No file provided"
        )
    
    if not is_allowed_file(file.filename):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File type not allowed. Only DOCX and TXT files are supported."
        )
    
    # Check file size
    content = await file.read()
    if len(content) > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File too large. Maximum size is {MAX_FILE_SIZE // (1024*1024)}MB"
        )
    
    # Reset file pointer
    await file.seek(0)
    
    # Generate unique job ID and filename
    job_id = str(uuid.uuid4())
    file_extension = get_file_extension(file.filename)
    unique_filename = f"{job_id}{file_extension}"
    file_path = os.path.join(UPLOAD_DIR, unique_filename)
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # Save file to disk
        await save_uploaded_file(file, file_path)
        
        # Extract text from file
        extracted_text = process_job_description_text(file_path, file_extension)
        processed_text = clean_job_description_text(extracted_text)
        
        if not processed_text:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Could not extract text from the uploaded file"
            )
        
        # Save to database
        cursor.execute('''
            INSERT INTO job_descriptions (
                id, user_id, title, company, description_text, 
                original_filename, file_path, file_type, processed_text
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            job_id, current_user.id, title, company, processed_text,
            file.filename, file_path, file_extension, processed_text
        ))
        
        conn.commit()
        
        return JobDescriptionUploadResponse(
            job_id=job_id,
            title=title,
            company=company,
            description_text=processed_text,
            file_type=file_extension,
            upload_status="uploaded",
            message="Job description uploaded and processed successfully"
        )
        
    except HTTPException:
        # Clean up file if there's an error
        if os.path.exists(file_path):
            os.remove(file_path)
        raise
    except Exception as e:
        # Clean up file if database operation fails
        if os.path.exists(file_path):
            os.remove(file_path)
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Upload failed: {str(e)}"
        )
    finally:
        conn.close()

@router.post("/upload-text", response_model=JobDescriptionUploadResponse)
async def upload_job_description_text(
    job_data: JobDescriptionText,
    current_user: CurrentUser = Depends(get_current_user)
):
    """
    Upload job description as plain text
    - Accepts plain text job description
    - Processes and cleans the text
    - Returns job ID and processed text
    """

    if not job_data.description_text.strip():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Job description text cannot be empty"
        )

    # Generate unique job ID
    job_id = str(uuid.uuid4())

    # Clean and process the text
    processed_text = clean_job_description_text(job_data.description_text)

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Save to database
        cursor.execute('''
            INSERT INTO job_descriptions (
                id, user_id, title, company, description_text,
                file_type, processed_text
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            job_id, current_user.id, job_data.title, job_data.company,
            processed_text, "text", processed_text
        ))

        conn.commit()

        return JobDescriptionUploadResponse(
            job_id=job_id,
            title=job_data.title,
            company=job_data.company,
            description_text=processed_text,
            file_type="text",
            upload_status="uploaded",
            message="Job description uploaded and processed successfully"
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Upload failed: {str(e)}"
        )
    finally:
        conn.close()

@router.get("/list", response_model=List[JobDescriptionInfo])
async def list_job_descriptions(current_user: CurrentUser = Depends(get_current_user)):
    """
    List all job descriptions uploaded by the current user
    """
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('''
            SELECT id, title, company, description_text, original_filename,
                   file_type, processed_text, created_at
            FROM job_descriptions
            WHERE user_id = ?
            ORDER BY created_at DESC
        ''', (current_user.id,))

        jobs = cursor.fetchall()

        return [
            JobDescriptionInfo(
                id=job['id'],
                title=job['title'],
                company=job['company'],
                description_text=job['description_text'],
                original_filename=job['original_filename'],
                file_type=job['file_type'],
                processed_text=job['processed_text'],
                created_at=datetime.fromisoformat(job['created_at'])
            )
            for job in jobs
        ]

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list job descriptions: {str(e)}"
        )
    finally:
        conn.close()

@router.get("/{job_id}", response_model=JobDescriptionInfo)
async def get_job_description(
    job_id: str,
    current_user: CurrentUser = Depends(get_current_user)
):
    """
    Get specific job description by job ID
    """
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('''
            SELECT id, title, company, description_text, original_filename,
                   file_type, processed_text, created_at
            FROM job_descriptions
            WHERE id = ? AND user_id = ?
        ''', (job_id, current_user.id))

        job = cursor.fetchone()

        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job description not found"
            )

        return JobDescriptionInfo(
            id=job['id'],
            title=job['title'],
            company=job['company'],
            description_text=job['description_text'],
            original_filename=job['original_filename'],
            file_type=job['file_type'],
            processed_text=job['processed_text'],
            created_at=datetime.fromisoformat(job['created_at'])
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get job description: {str(e)}"
        )
    finally:
        conn.close()

@router.delete("/{job_id}")
async def delete_job_description(
    job_id: str,
    current_user: CurrentUser = Depends(get_current_user)
):
    """
    Delete a job description by job ID
    """
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Get job description info first
        cursor.execute('''
            SELECT file_path FROM job_descriptions
            WHERE id = ? AND user_id = ?
        ''', (job_id, current_user.id))

        job = cursor.fetchone()

        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job description not found"
            )

        # Delete from database
        cursor.execute(
            "DELETE FROM job_descriptions WHERE id = ? AND user_id = ?",
            (job_id, current_user.id)
        )

        # Delete file from disk if it exists
        if job['file_path'] and os.path.exists(job['file_path']):
            os.remove(job['file_path'])

        conn.commit()

        return {"message": "Job description deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete job description: {str(e)}"
        )
    finally:
        conn.close()
