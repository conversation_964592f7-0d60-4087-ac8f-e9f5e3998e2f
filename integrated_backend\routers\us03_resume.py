"""
US-03: Resume Upload Router
Handles resume file uploads (PDF & DOCX)
"""

from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, status
from pydantic import BaseModel
import os
import uuid
from datetime import datetime
import aiofiles
from typing import List, Optional
import PyPDF2
from docx import Document
import io

from database.database import get_db_connection
from routers.us02_login import get_current_user, CurrentUser

router = APIRouter()

# Configuration
UPLOAD_DIR = os.path.join(os.path.dirname(__file__), "..", "uploads", "resumes")
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
ALLOWED_EXTENSIONS = {".pdf", ".docx"}

# Ensure upload directory exists
os.makedirs(UPLOAD_DIR, exist_ok=True)

# Pydantic models
class ResumeUploadResponse(BaseModel):
    document_id: str
    filename: str
    file_size: int
    file_type: str
    upload_status: str
    message: str

class ResumeInfo(BaseModel):
    id: str
    filename: str
    original_filename: str
    file_type: str
    file_size: int
    upload_status: str
    created_at: datetime
    processed_text: Optional[str] = None

# Utility functions
def get_file_extension(filename: str) -> str:
    """Get file extension from filename"""
    return os.path.splitext(filename)[1].lower()

def is_allowed_file(filename: str) -> bool:
    """Check if file extension is allowed"""
    return get_file_extension(filename) in ALLOWED_EXTENSIONS

async def save_uploaded_file(upload_file: UploadFile, file_path: str):
    """Save uploaded file to disk"""
    async with aiofiles.open(file_path, 'wb') as f:
        content = await upload_file.read()
        await f.write(content)
    return len(content)

def extract_text_from_pdf(file_path: str) -> str:
    """Extract text from PDF file"""
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        return text.strip()
    except Exception as e:
        print(f"Error extracting text from PDF: {e}")
        return ""

def extract_text_from_docx(file_path: str) -> str:
    """Extract text from DOCX file"""
    try:
        doc = Document(file_path)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text.strip()
    except Exception as e:
        print(f"Error extracting text from DOCX: {e}")
        return ""

def process_resume_text(file_path: str, file_type: str) -> str:
    """Process resume and extract text based on file type"""
    if file_type == ".pdf":
        return extract_text_from_pdf(file_path)
    elif file_type == ".docx":
        return extract_text_from_docx(file_path)
    else:
        return ""

# API Endpoints
@router.post("/upload", response_model=ResumeUploadResponse)
async def upload_resume(
    file: UploadFile = File(...),
    current_user: CurrentUser = Depends(get_current_user)
):
    """
    Upload resume file (PDF or DOCX)
    - Validates file type and size
    - Saves file to disk
    - Extracts text content
    - Returns document ID and success status
    """
    
    # Validate file
    if not file.filename:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No file provided"
        )
    
    if not is_allowed_file(file.filename):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File type not allowed. Only PDF and DOCX files are supported."
        )
    
    # Check file size
    content = await file.read()
    if len(content) > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File too large. Maximum size is {MAX_FILE_SIZE // (1024*1024)}MB"
        )
    
    # Reset file pointer
    await file.seek(0)
    
    # Generate unique document ID and filename
    document_id = str(uuid.uuid4())
    file_extension = get_file_extension(file.filename)
    unique_filename = f"{document_id}{file_extension}"
    file_path = os.path.join(UPLOAD_DIR, unique_filename)
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # Save file to disk
        file_size = await save_uploaded_file(file, file_path)
        
        # Extract text from file
        processed_text = process_resume_text(file_path, file_extension)
        
        # Save to database
        cursor.execute('''
            INSERT INTO resume_uploads (
                id, user_id, filename, original_filename, file_path, 
                file_type, file_size, upload_status, processed_text
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            document_id, current_user.id, unique_filename, file.filename,
            file_path, file_extension, file_size, "uploaded", processed_text
        ))
        
        conn.commit()
        
        return ResumeUploadResponse(
            document_id=document_id,
            filename=unique_filename,
            file_size=file_size,
            file_type=file_extension,
            upload_status="uploaded",
            message="Resume uploaded and processed successfully"
        )
        
    except Exception as e:
        # Clean up file if database operation fails
        if os.path.exists(file_path):
            os.remove(file_path)
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Upload failed: {str(e)}"
        )
    finally:
        conn.close()

@router.get("/list", response_model=List[ResumeInfo])
async def list_resumes(current_user: CurrentUser = Depends(get_current_user)):
    """
    List all resumes uploaded by the current user
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT id, filename, original_filename, file_type, file_size, 
                   upload_status, created_at, processed_text
            FROM resume_uploads 
            WHERE user_id = ? 
            ORDER BY created_at DESC
        ''', (current_user.id,))
        
        resumes = cursor.fetchall()
        
        return [
            ResumeInfo(
                id=resume['id'],
                filename=resume['filename'],
                original_filename=resume['original_filename'],
                file_type=resume['file_type'],
                file_size=resume['file_size'],
                upload_status=resume['upload_status'],
                created_at=datetime.fromisoformat(resume['created_at']),
                processed_text=resume['processed_text']
            )
            for resume in resumes
        ]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list resumes: {str(e)}"
        )
    finally:
        conn.close()

@router.get("/{document_id}", response_model=ResumeInfo)
async def get_resume(
    document_id: str,
    current_user: CurrentUser = Depends(get_current_user)
):
    """
    Get specific resume by document ID
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT id, filename, original_filename, file_type, file_size, 
                   upload_status, created_at, processed_text
            FROM resume_uploads 
            WHERE id = ? AND user_id = ?
        ''', (document_id, current_user.id))
        
        resume = cursor.fetchone()
        
        if not resume:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume not found"
            )
        
        return ResumeInfo(
            id=resume['id'],
            filename=resume['filename'],
            original_filename=resume['original_filename'],
            file_type=resume['file_type'],
            file_size=resume['file_size'],
            upload_status=resume['upload_status'],
            created_at=datetime.fromisoformat(resume['created_at']),
            processed_text=resume['processed_text']
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get resume: {str(e)}"
        )
    finally:
        conn.close()

@router.delete("/{document_id}")
async def delete_resume(
    document_id: str,
    current_user: CurrentUser = Depends(get_current_user)
):
    """
    Delete a resume by document ID
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # Get resume info first
        cursor.execute('''
            SELECT file_path FROM resume_uploads 
            WHERE id = ? AND user_id = ?
        ''', (document_id, current_user.id))
        
        resume = cursor.fetchone()
        
        if not resume:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Resume not found"
            )
        
        # Delete from database
        cursor.execute(
            "DELETE FROM resume_uploads WHERE id = ? AND user_id = ?",
            (document_id, current_user.id)
        )
        
        # Delete file from disk
        if os.path.exists(resume['file_path']):
            os.remove(resume['file_path'])
        
        conn.commit()
        
        return {"message": "Resume deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete resume: {str(e)}"
        )
    finally:
        conn.close()
