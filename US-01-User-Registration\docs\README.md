# US-01 User Registration - Documentation

## Overview
This microservice handles user authentication and registration for the OpenResume AI application. It provides secure user registration with email verification and login functionality.

## Features
- User registration with email/password
- Password validation (strength requirements)
- Email verification system
- Secure password hashing using bcrypt
- JWT token-based authentication
- Input validation and sanitization

## API Endpoints

### 1. POST /register
**Purpose**: Register a new user account

**Request Body**:
```json
{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "confirm_password": "SecurePass123!"
}
```

**Password Requirements**:
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one digit
- At least one special character

**Response**:
```json
{
    "message": "User registered successfully",
    "email_sent": true,
    "user_id": "uuid-string"
}
```

**Line-by-line Explanation**:
1. **Line 1-10**: Import necessary libraries for FastAPI, security, email, and database operations
2. **Line 12-18**: Initialize FastAPI app with title, description, and version
3. **Line 20-28**: Configure CORS middleware to allow cross-origin requests from frontend
4. **Line 30-35**: Set up security configuration including JWT secret key and token expiration
5. **Line 37-43**: Configure email SMTP settings for sending verification emails
6. **Line 45-70**: Define Pydantic models for request/response validation
7. **Line 72-85**: Password validation function checking strength requirements
8. **Line 87-95**: Database connection and initialization functions
9. **Line 97-105**: Password hashing and verification utility functions
10. **Line 107-115**: JWT token creation function
11. **Line 117-140**: Email sending function for verification
12. **Line 142-180**: User registration endpoint implementation
13. **Line 182-220**: User login endpoint implementation
14. **Line 222-250**: Email verification endpoint implementation

### 2. POST /login
**Purpose**: Authenticate user and return JWT token

**Request Body**:
```json
{
    "email": "<EMAIL>",
    "password": "SecurePass123!"
}
```

**Response**:
```json
{
    "access_token": "jwt-token-string",
    "token_type": "bearer"
}
```

### 3. GET /verify-email?token=verification-token
**Purpose**: Verify user email address using verification token

**Response**:
```json
{
    "message": "Email verified successfully"
}
```

### 4. GET /health
**Purpose**: Health check endpoint for monitoring

**Response**:
```json
{
    "status": "healthy",
    "service": "US-01 User Registration"
}
```

## Database Schema

### Users Table
```sql
CREATE TABLE users (
    id TEXT PRIMARY KEY,                    -- UUID for user identification
    email TEXT UNIQUE NOT NULL,             -- User email (unique constraint)
    password_hash TEXT NOT NULL,            -- Bcrypt hashed password
    is_verified BOOLEAN DEFAULT FALSE,      -- Email verification status
    verification_token TEXT,                -- Token for email verification
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- Account creation time
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP   -- Last update time
);
```

## Security Features

1. **Password Hashing**: Uses bcrypt with salt for secure password storage
2. **JWT Tokens**: Stateless authentication with configurable expiration
3. **Email Verification**: Prevents fake email registrations
4. **Input Validation**: Pydantic models ensure data integrity
5. **SQL Injection Protection**: Parameterized queries prevent SQL injection

## Environment Variables

Create a `.env` file with the following variables:
```
SECRET_KEY=your-super-secret-jwt-key
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your-app-password
```

## Error Handling

The API returns appropriate HTTP status codes:
- **200**: Success
- **400**: Bad Request (validation errors, user already exists)
- **401**: Unauthorized (invalid credentials, unverified email)
- **500**: Internal Server Error

## Testing

Run the service:
```bash
cd US-01-User-Registration/backend
pip install -r requirements.txt
python app.py
```

The API will be available at `http://localhost:8001`
API documentation at `http://localhost:8001/docs`

## Integration with Frontend

The frontend should:
1. Send registration requests to `/register`
2. Display email verification message
3. Handle verification link clicks
4. Store JWT tokens for authenticated requests
5. Include tokens in Authorization header: `Bearer <token>`
