"""
US-07: Suggestions Router
Provides improvement suggestions based on resume-job description analysis
"""

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import List, Dict, Optional, Any
import uuid
from datetime import datetime

from database.database import get_db_connection
from routers.us02_login import get_current_user, CurrentUser

router = APIRouter()

# Pydantic models
class SuggestionRequest(BaseModel):
    match_id: Optional[str] = None
    resume_id: Optional[str] = None
    job_description_id: Optional[str] = None

class Suggestion(BaseModel):
    category: str
    priority: str  # 'high', 'medium', 'low'
    title: str
    description: str
    action_items: List[str]
    impact_score: float

class SuggestionsResponse(BaseModel):
    suggestion_id: str
    resume_id: str
    job_description_id: str
    overall_match_score: float
    suggestions: List[Suggestion]
    missing_keywords: List[str]
    priority_skills: List[str]
    quick_wins: List[str]
    created_at: datetime

class SuggestionHistoryResponse(BaseModel):
    id: str
    resume_id: str
    job_description_id: str
    suggestion_count: int
    high_priority_count: int
    created_at: datetime

# Utility functions
def get_match_analysis(match_id: str, user_id: str) -> Dict[str, Any]:
    """Get match analysis results"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT * FROM analysis_results 
            WHERE id = ? AND user_id = ? AND analysis_type = 'matching_score'
        ''', (match_id, user_id))
        
        result = cursor.fetchone()
        if not result:
            raise ValueError(f"Match analysis not found: {match_id}")
        
        # Parse results
        try:
            results = eval(result['results_json'])
            return {
                'resume_id': result['resume_id'],
                'job_description_id': result['job_description_id'],
                'overall_score': result['score'],
                'results': results
            }
        except:
            raise ValueError("Invalid match analysis data")
            
    finally:
        conn.close()

def create_match_and_analyze(resume_id: str, job_description_id: str, user_id: str) -> Dict[str, Any]:
    """Create a new match analysis if one doesn't exist"""
    # This would typically call the matching service
    # For now, return a placeholder
    return {
        'resume_id': resume_id,
        'job_description_id': job_description_id,
        'overall_score': 0.0,
        'results': {
            'missing_keywords': [],
            'skill_gaps': {},
            'detailed_scores': {}
        }
    }

def generate_keyword_suggestions(missing_keywords: List[str]) -> List[Suggestion]:
    """Generate suggestions based on missing keywords"""
    suggestions = []
    
    if not missing_keywords:
        return suggestions
    
    # Group keywords by importance
    high_priority_keywords = missing_keywords[:3]
    medium_priority_keywords = missing_keywords[3:6]
    
    if high_priority_keywords:
        suggestions.append(Suggestion(
            category="Keywords",
            priority="high",
            title="Add Critical Missing Keywords",
            description=f"Your resume is missing {len(high_priority_keywords)} important keywords that appear in the job description.",
            action_items=[
                f"Include '{keyword}' in your resume where relevant" for keyword in high_priority_keywords
            ],
            impact_score=0.8
        ))
    
    if medium_priority_keywords:
        suggestions.append(Suggestion(
            category="Keywords",
            priority="medium",
            title="Enhance Keyword Coverage",
            description=f"Consider adding these relevant keywords to improve your match score.",
            action_items=[
                f"Incorporate '{keyword}' in your experience descriptions" for keyword in medium_priority_keywords
            ],
            impact_score=0.5
        ))
    
    return suggestions

def generate_skill_suggestions(skill_gaps: Dict[str, List[str]]) -> List[Suggestion]:
    """Generate suggestions based on skill gaps"""
    suggestions = []
    
    # Priority mapping for skill categories
    priority_map = {
        'programming_languages': 'high',
        'frameworks': 'high',
        'databases': 'medium',
        'cloud_platforms': 'medium',
        'tools': 'low'
    }
    
    for category, missing_skills in skill_gaps.items():
        if not missing_skills:
            continue
            
        priority = priority_map.get(category, 'medium')
        category_name = category.replace('_', ' ').title()
        
        # Limit to top 3 skills per category
        top_skills = missing_skills[:3]
        
        suggestions.append(Suggestion(
            category="Technical Skills",
            priority=priority,
            title=f"Develop {category_name} Skills",
            description=f"The job requires {category_name.lower()} skills that are not evident in your resume.",
            action_items=[
                f"Learn {skill}" for skill in top_skills
            ] + [
                f"Add {category_name.lower()} projects to your portfolio",
                f"Consider online courses or certifications in {category_name.lower()}"
            ],
            impact_score=0.9 if priority == 'high' else (0.6 if priority == 'medium' else 0.3)
        ))
    
    return suggestions

def generate_score_based_suggestions(detailed_scores: Dict[str, float]) -> List[Suggestion]:
    """Generate suggestions based on detailed scores"""
    suggestions = []
    
    # Identify areas with low scores
    low_score_threshold = 30.0
    medium_score_threshold = 60.0
    
    for area, score in detailed_scores.items():
        if score < low_score_threshold:
            area_name = area.replace('_', ' ').title()
            suggestions.append(Suggestion(
                category="Score Improvement",
                priority="high",
                title=f"Improve {area_name} Match",
                description=f"Your {area_name.lower()} score is {score:.1f}%, which is below the recommended threshold.",
                action_items=[
                    f"Review the job requirements for {area_name.lower()}",
                    f"Highlight relevant {area_name.lower()} experience in your resume",
                    f"Consider adding specific examples of {area_name.lower()} work"
                ],
                impact_score=0.7
            ))
        elif score < medium_score_threshold:
            area_name = area.replace('_', ' ').title()
            suggestions.append(Suggestion(
                category="Score Improvement",
                priority="medium",
                title=f"Enhance {area_name} Presentation",
                description=f"Your {area_name.lower()} score is {score:.1f}%. There's room for improvement.",
                action_items=[
                    f"Provide more specific examples of {area_name.lower()} achievements",
                    f"Use metrics and numbers to quantify {area_name.lower()} impact"
                ],
                impact_score=0.4
            ))
    
    return suggestions

def generate_quick_wins(missing_keywords: List[str], skill_gaps: Dict[str, List[str]]) -> List[str]:
    """Generate quick win suggestions"""
    quick_wins = []
    
    # Easy keyword additions
    if missing_keywords:
        quick_wins.append(f"Add '{missing_keywords[0]}' to your skills section")
    
    # Tool-based quick wins
    tool_gaps = skill_gaps.get('tools', [])
    if tool_gaps:
        quick_wins.append(f"Mention experience with {tool_gaps[0]} if applicable")
    
    # General quick wins
    quick_wins.extend([
        "Update your resume summary to match job requirements",
        "Use action verbs that appear in the job description",
        "Quantify your achievements with specific numbers"
    ])
    
    return quick_wins[:5]  # Limit to top 5

# API Endpoints
@router.post("/generate", response_model=SuggestionsResponse)
async def generate_suggestions(
    request: SuggestionRequest,
    current_user: CurrentUser = Depends(get_current_user)
):
    """
    Generate improvement suggestions based on resume-job description analysis
    - Analyzes gaps between resume and job requirements
    - Provides prioritized suggestions for improvement
    - Includes quick wins and action items
    - Limits suggestions to top 5 most impactful
    """
    
    try:
        # Get match analysis
        if request.match_id:
            try:
                match_data = get_match_analysis(request.match_id, current_user.id)
            except ValueError as e:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=str(e)
                )
        elif request.resume_id and request.job_description_id:
            # Create new analysis if needed
            match_data = create_match_and_analyze(
                request.resume_id, 
                request.job_description_id, 
                current_user.id
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Either match_id or both resume_id and job_description_id must be provided"
            )
        
        # Extract analysis data
        results = match_data['results']
        missing_keywords = results.get('missing_keywords', [])
        skill_gaps = results.get('skill_gaps', {})
        detailed_scores = results.get('detailed_scores', {})
        
        # Generate suggestions
        all_suggestions = []
        
        # Keyword-based suggestions
        all_suggestions.extend(generate_keyword_suggestions(missing_keywords))
        
        # Skill-based suggestions
        all_suggestions.extend(generate_skill_suggestions(skill_gaps))
        
        # Score-based suggestions
        all_suggestions.extend(generate_score_based_suggestions(detailed_scores))
        
        # Sort by impact score and priority, limit to top 5
        all_suggestions.sort(key=lambda x: (
            {'high': 3, 'medium': 2, 'low': 1}[x.priority],
            x.impact_score
        ), reverse=True)
        
        top_suggestions = all_suggestions[:5]
        
        # Generate quick wins and priority skills
        quick_wins = generate_quick_wins(missing_keywords, skill_gaps)
        priority_skills = []
        for category, skills in skill_gaps.items():
            if category in ['programming_languages', 'frameworks']:
                priority_skills.extend(skills[:2])
        
        # Generate suggestion ID and save
        suggestion_id = str(uuid.uuid4())
        
        # Save suggestions to database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        try:
            suggestion_data = {
                'suggestions': [s.dict() for s in top_suggestions],
                'missing_keywords': missing_keywords,
                'priority_skills': priority_skills,
                'quick_wins': quick_wins
            }
            
            cursor.execute('''
                INSERT INTO analysis_results (
                    id, user_id, resume_id, job_description_id, analysis_type, results_json
                ) VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                suggestion_id, current_user.id, 
                match_data['resume_id'], match_data['job_description_id'],
                'suggestions', str(suggestion_data)
            ))
            
            conn.commit()
            
        finally:
            conn.close()
        
        return SuggestionsResponse(
            suggestion_id=suggestion_id,
            resume_id=match_data['resume_id'],
            job_description_id=match_data['job_description_id'],
            overall_match_score=match_data['overall_score'],
            suggestions=top_suggestions,
            missing_keywords=missing_keywords[:10],  # Limit display
            priority_skills=priority_skills,
            quick_wins=quick_wins,
            created_at=datetime.now()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Suggestion generation failed: {str(e)}"
        )

@router.get("/history", response_model=List[SuggestionHistoryResponse])
async def get_suggestion_history(current_user: CurrentUser = Depends(get_current_user)):
    """
    Get suggestion history for the current user
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT id, resume_id, job_description_id, results_json, created_at
            FROM analysis_results 
            WHERE user_id = ? AND analysis_type = 'suggestions'
            ORDER BY created_at DESC
        ''', (current_user.id,))
        
        suggestions = cursor.fetchall()
        
        result = []
        for suggestion in suggestions:
            try:
                data = eval(suggestion['results_json'])
                suggestion_count = len(data.get('suggestions', []))
                high_priority_count = len([s for s in data.get('suggestions', []) if s.get('priority') == 'high'])
            except:
                suggestion_count = 0
                high_priority_count = 0
            
            result.append(SuggestionHistoryResponse(
                id=suggestion['id'],
                resume_id=suggestion['resume_id'],
                job_description_id=suggestion['job_description_id'],
                suggestion_count=suggestion_count,
                high_priority_count=high_priority_count,
                created_at=datetime.fromisoformat(suggestion['created_at'])
            ))
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get suggestion history: {str(e)}"
        )
    finally:
        conn.close()

@router.get("/{suggestion_id}")
async def get_suggestion_details(
    suggestion_id: str,
    current_user: CurrentUser = Depends(get_current_user)
):
    """
    Get detailed results of specific suggestions
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT * FROM analysis_results 
            WHERE id = ? AND user_id = ? AND analysis_type = 'suggestions'
        ''', (suggestion_id, current_user.id))
        
        suggestion = cursor.fetchone()
        
        if not suggestion:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Suggestions not found"
            )
        
        return {
            "id": suggestion['id'],
            "resume_id": suggestion['resume_id'],
            "job_description_id": suggestion['job_description_id'],
            "results": suggestion['results_json'],
            "created_at": suggestion['created_at']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get suggestion details: {str(e)}"
        )
    finally:
        conn.close()
