# Integrated Frontend

This folder contains all frontend components and pages for the OpenResume AI application.

## Structure
- Components: Reusable UI components
- Pages: Individual page components
- Services: API communication services
- Utils: Utility functions and helpers
- Assets: Static assets (images, styles, etc.)

## Technology Stack
- React.js
- TypeScript
- Tailwind CSS
- Axios for API calls

## Getting Started
1. Install dependencies: `npm install`
2. Start development server: `npm start`
3. Build for production: `npm run build`
