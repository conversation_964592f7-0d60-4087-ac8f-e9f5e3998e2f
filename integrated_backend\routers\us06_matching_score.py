"""
US-06: Matching Score Router
Calculates match score between resume and job description
"""

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import List, Dict, Optional, Any
import uuid
from datetime import datetime
import json

from database.database import get_db_connection
from routers.us02_login import get_current_user, CurrentUser
from utils.nlp_utils import comprehensive_keyword_extraction

router = APIRouter()

# Pydantic models
class MatchingRequest(BaseModel):
    resume_id: str
    job_description_id: str

class MatchingScoreResponse(BaseModel):
    match_id: str
    resume_id: str
    job_description_id: str
    overall_score: float
    detailed_scores: Dict[str, float]
    matching_keywords: List[str]
    missing_keywords: List[str]
    skill_matches: Dict[str, List[str]]
    skill_gaps: Dict[str, List[str]]
    recommendations: List[str]
    created_at: datetime

class MatchingHistoryResponse(BaseModel):
    id: str
    resume_id: str
    job_description_id: str
    overall_score: float
    created_at: datetime
    resume_filename: Optional[str]
    job_title: Optional[str]

# Utility functions
def get_document_analysis(document_id: str, document_type: str, user_id: str) -> Dict[str, Any]:
    """Get or create analysis for a document"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # First, try to get existing analysis
        if document_type == 'resume':
            cursor.execute('''
                SELECT results_json FROM analysis_results 
                WHERE resume_id = ? AND user_id = ? AND analysis_type = 'keyword_extraction'
                ORDER BY created_at DESC LIMIT 1
            ''', (document_id, user_id))
        else:
            cursor.execute('''
                SELECT results_json FROM analysis_results 
                WHERE job_description_id = ? AND user_id = ? AND analysis_type = 'keyword_extraction'
                ORDER BY created_at DESC LIMIT 1
            ''', (document_id, user_id))
        
        result = cursor.fetchone()
        
        if result:
            # Parse existing analysis
            try:
                return eval(result['results_json'])  # Convert string back to dict
            except:
                pass
        
        # If no analysis exists, get document text and analyze
        if document_type == 'resume':
            cursor.execute('''
                SELECT processed_text FROM resume_uploads 
                WHERE id = ? AND user_id = ?
            ''', (document_id, user_id))
        else:
            cursor.execute('''
                SELECT processed_text FROM job_descriptions 
                WHERE id = ? AND user_id = ?
            ''', (document_id, user_id))
        
        doc_result = cursor.fetchone()
        if not doc_result:
            raise ValueError(f"Document not found: {document_id}")
        
        # Perform analysis
        text = doc_result['processed_text'] or ""
        analysis = comprehensive_keyword_extraction(text)
        
        # Save analysis for future use
        analysis_id = str(uuid.uuid4())
        cursor.execute('''
            INSERT INTO analysis_results (
                id, user_id, resume_id, job_description_id, analysis_type, results_json
            ) VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            analysis_id, user_id,
            document_id if document_type == 'resume' else None,
            document_id if document_type == 'job_description' else None,
            'keyword_extraction',
            str(analysis)
        ))
        conn.commit()
        
        return analysis
        
    finally:
        conn.close()

def calculate_keyword_similarity(resume_keywords: List[tuple], job_keywords: List[tuple]) -> Dict[str, Any]:
    """Calculate keyword similarity between resume and job description"""
    # Extract keyword strings (ignore scores for now)
    resume_kw_set = set([kw[0].lower() for kw in resume_keywords])
    job_kw_set = set([kw[0].lower() for kw in job_keywords])
    
    # Find matches and gaps
    matching_keywords = list(resume_kw_set.intersection(job_kw_set))
    missing_keywords = list(job_kw_set - resume_kw_set)
    
    # Calculate similarity score
    if len(job_kw_set) == 0:
        keyword_score = 0.0
    else:
        keyword_score = len(matching_keywords) / len(job_kw_set)
    
    return {
        'score': keyword_score,
        'matching': matching_keywords,
        'missing': missing_keywords
    }

def calculate_skill_similarity(resume_skills: Dict[str, List[str]], job_skills: Dict[str, List[str]]) -> Dict[str, Any]:
    """Calculate skill similarity between resume and job description"""
    skill_matches = {}
    skill_gaps = {}
    category_scores = {}
    
    # Get all skill categories
    all_categories = set(resume_skills.keys()) | set(job_skills.keys())
    
    for category in all_categories:
        resume_cat_skills = set([skill.lower() for skill in resume_skills.get(category, [])])
        job_cat_skills = set([skill.lower() for skill in job_skills.get(category, [])])
        
        # Find matches and gaps
        matches = list(resume_cat_skills.intersection(job_cat_skills))
        gaps = list(job_cat_skills - resume_cat_skills)
        
        if matches:
            skill_matches[category] = matches
        if gaps:
            skill_gaps[category] = gaps
        
        # Calculate category score
        if len(job_cat_skills) == 0:
            category_scores[category] = 1.0 if len(resume_cat_skills) > 0 else 0.0
        else:
            category_scores[category] = len(matches) / len(job_cat_skills)
    
    # Overall skill score (weighted average)
    if category_scores:
        overall_skill_score = sum(category_scores.values()) / len(category_scores)
    else:
        overall_skill_score = 0.0
    
    return {
        'score': overall_skill_score,
        'matches': skill_matches,
        'gaps': skill_gaps,
        'category_scores': category_scores
    }

def generate_recommendations(skill_gaps: Dict[str, List[str]], missing_keywords: List[str]) -> List[str]:
    """Generate improvement recommendations"""
    recommendations = []
    
    # Skill-based recommendations
    for category, gaps in skill_gaps.items():
        if gaps:
            category_name = category.replace('_', ' ').title()
            recommendations.append(f"Consider learning {category_name}: {', '.join(gaps[:3])}")
    
    # Keyword-based recommendations
    if missing_keywords:
        top_missing = missing_keywords[:5]
        recommendations.append(f"Include these relevant keywords: {', '.join(top_missing)}")
    
    # General recommendations
    if len(recommendations) == 0:
        recommendations.append("Great match! Consider highlighting your relevant experience more prominently.")
    elif len(skill_gaps) > 3:
        recommendations.append("Focus on developing skills in the most critical areas first.")
    
    return recommendations[:5]  # Limit to top 5 recommendations

def calculate_overall_score(keyword_score: float, skill_score: float, role_match: float) -> float:
    """Calculate weighted overall matching score"""
    # Weighted combination: skills (50%), keywords (30%), roles (20%)
    overall = (skill_score * 0.5) + (keyword_score * 0.3) + (role_match * 0.2)
    return round(min(100.0, overall * 100), 2)

# API Endpoints
@router.post("/calculate-match", response_model=MatchingScoreResponse)
async def calculate_matching_score(
    request: MatchingRequest,
    current_user: CurrentUser = Depends(get_current_user)
):
    """
    Calculate match score between resume and job description
    - Analyzes keyword overlap
    - Compares technical and soft skills
    - Provides detailed scoring breakdown
    - Returns improvement recommendations
    """
    
    try:
        # Get analyses for both documents
        try:
            resume_analysis = get_document_analysis(request.resume_id, 'resume', current_user.id)
            job_analysis = get_document_analysis(request.job_description_id, 'job_description', current_user.id)
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        
        # Calculate keyword similarity
        keyword_similarity = calculate_keyword_similarity(
            resume_analysis.get('tfidf_keywords', []),
            job_analysis.get('tfidf_keywords', [])
        )
        
        # Calculate skill similarity
        skill_similarity = calculate_skill_similarity(
            resume_analysis.get('technical_skills', {}),
            job_analysis.get('technical_skills', {})
        )
        
        # Calculate role matching
        resume_roles = set([role.lower() for role in resume_analysis.get('job_roles', [])])
        job_roles = set([role.lower() for role in job_analysis.get('job_roles', [])])
        
        if len(job_roles) == 0:
            role_match_score = 0.5  # Neutral if no roles specified
        else:
            role_matches = len(resume_roles.intersection(job_roles))
            role_match_score = role_matches / len(job_roles)
        
        # Calculate overall score
        overall_score = calculate_overall_score(
            keyword_similarity['score'],
            skill_similarity['score'],
            role_match_score
        )
        
        # Generate recommendations
        recommendations = generate_recommendations(
            skill_similarity['gaps'],
            keyword_similarity['missing']
        )
        
        # Create detailed scores
        detailed_scores = {
            'keyword_match': round(keyword_similarity['score'] * 100, 2),
            'skill_match': round(skill_similarity['score'] * 100, 2),
            'role_match': round(role_match_score * 100, 2),
            'technical_skills': round(skill_similarity['category_scores'].get('programming_languages', 0) * 100, 2),
            'frameworks': round(skill_similarity['category_scores'].get('frameworks', 0) * 100, 2),
            'tools': round(skill_similarity['category_scores'].get('tools', 0) * 100, 2)
        }
        
        # Generate match ID and save results
        match_id = str(uuid.uuid4())
        
        # Save matching results
        conn = get_db_connection()
        cursor = conn.cursor()
        
        try:
            match_results = {
                'overall_score': overall_score,
                'detailed_scores': detailed_scores,
                'matching_keywords': keyword_similarity['matching'],
                'missing_keywords': keyword_similarity['missing'],
                'skill_matches': skill_similarity['matches'],
                'skill_gaps': skill_similarity['gaps'],
                'recommendations': recommendations
            }
            
            cursor.execute('''
                INSERT INTO analysis_results (
                    id, user_id, resume_id, job_description_id, analysis_type, results_json, score
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                match_id, current_user.id, request.resume_id, request.job_description_id,
                'matching_score', str(match_results), overall_score
            ))
            
            conn.commit()
            
        finally:
            conn.close()
        
        return MatchingScoreResponse(
            match_id=match_id,
            resume_id=request.resume_id,
            job_description_id=request.job_description_id,
            overall_score=overall_score,
            detailed_scores=detailed_scores,
            matching_keywords=keyword_similarity['matching'],
            missing_keywords=keyword_similarity['missing'],
            skill_matches=skill_similarity['matches'],
            skill_gaps=skill_similarity['gaps'],
            recommendations=recommendations,
            created_at=datetime.now()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Matching calculation failed: {str(e)}"
        )

@router.get("/matches", response_model=List[MatchingHistoryResponse])
async def get_matching_history(current_user: CurrentUser = Depends(get_current_user)):
    """
    Get matching history for the current user
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT ar.id, ar.resume_id, ar.job_description_id, ar.score, ar.created_at,
                   ru.original_filename as resume_filename, jd.title as job_title
            FROM analysis_results ar
            LEFT JOIN resume_uploads ru ON ar.resume_id = ru.id
            LEFT JOIN job_descriptions jd ON ar.job_description_id = jd.id
            WHERE ar.user_id = ? AND ar.analysis_type = 'matching_score'
            ORDER BY ar.created_at DESC
        ''', (current_user.id,))
        
        matches = cursor.fetchall()
        
        return [
            MatchingHistoryResponse(
                id=match['id'],
                resume_id=match['resume_id'],
                job_description_id=match['job_description_id'],
                overall_score=match['score'] or 0.0,
                created_at=datetime.fromisoformat(match['created_at']),
                resume_filename=match['resume_filename'],
                job_title=match['job_title']
            )
            for match in matches
        ]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get matching history: {str(e)}"
        )
    finally:
        conn.close()

@router.get("/match/{match_id}")
async def get_match_details(
    match_id: str,
    current_user: CurrentUser = Depends(get_current_user)
):
    """
    Get detailed results of a specific match
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT * FROM analysis_results 
            WHERE id = ? AND user_id = ? AND analysis_type = 'matching_score'
        ''', (match_id, current_user.id))
        
        match = cursor.fetchone()
        
        if not match:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Match not found"
            )
        
        return {
            "id": match['id'],
            "resume_id": match['resume_id'],
            "job_description_id": match['job_description_id'],
            "overall_score": match['score'],
            "results": match['results_json'],
            "created_at": match['created_at']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get match details: {str(e)}"
        )
    finally:
        conn.close()
