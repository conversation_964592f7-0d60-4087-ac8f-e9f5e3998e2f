# PowerShell script to clean up old backend folder
# Run this script when no processes are using the backend folder

Write-Host "Cleaning up old backend folder..." -ForegroundColor Yellow

# Try to remove the old backend folder
try {
    if (Test-Path "backend") {
        Remove-Item -Recurse -Force "backend"
        Write-Host "✓ Old backend folder removed successfully!" -ForegroundColor Green
    } else {
        Write-Host "✓ Old backend folder already removed!" -ForegroundColor Green
    }
} catch {
    Write-Host "✗ Could not remove backend folder. It may be in use by another process." -ForegroundColor Red
    Write-Host "Please close any editors or terminals that might be using the backend folder and try again." -ForegroundColor Yellow
}

Write-Host "`nCurrent project structure:" -ForegroundColor Cyan
Get-ChildItem -Directory | ForEach-Object { Write-Host "  📁 $($_.Name)" -ForegroundColor Blue }

Write-Host "`nTo start the US-01 User Registration service:" -ForegroundColor Cyan
Write-Host "  cd US-01-User-Registration/backend" -ForegroundColor White
Write-Host "  pip install -r requirements.txt" -ForegroundColor White
Write-Host "  python app.py" -ForegroundColor White
