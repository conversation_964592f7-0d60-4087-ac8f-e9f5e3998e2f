"""
US-02: Authentication Router
Handles user login and JWT token management
"""

from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr
import bcrypt
import jwt
import os
from datetime import datetime, timedelta
import uuid
from typing import Optional

from database.database import get_db_connection

router = APIRouter()
security = HTTPBearer()

# Configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Pydantic models
class UserLogin(BaseModel):
    email: EmailStr
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str
    expires_in: int
    user_id: str

class TokenData(BaseModel):
    email: Optional[str] = None
    user_id: Optional[str] = None

class CurrentUser(BaseModel):
    id: str
    email: str
    is_verified: bool

# Utility functions
def verify_password(password: str, hashed: str) -> bool:
    """Verify password against hash"""
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str):
    """Verify JWT token"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")
        user_id: str = payload.get("user_id")
        if email is None or user_id is None:
            return None
        return TokenData(email=email, user_id=user_id)
    except jwt.PyJWTError:
        return None

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get current authenticated user"""
    token = credentials.credentials
    token_data = verify_token(token)
    
    if token_data is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute(
            "SELECT id, email, is_verified FROM users WHERE email = ?",
            (token_data.email,)
        )
        user = cursor.fetchone()
        
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return CurrentUser(
            id=user['id'],
            email=user['email'],
            is_verified=user['is_verified']
        )
    finally:
        conn.close()

# API Endpoints
@router.post("/login", response_model=Token)
async def login_user(user_data: UserLogin):
    """
    Login user
    - Validates credentials
    - Checks if email is verified
    - Returns JWT token
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # Get user from database
        cursor.execute(
            "SELECT id, email, password_hash, is_verified FROM users WHERE email = ?",
            (user_data.email,)
        )
        user = cursor.fetchone()
        
        if not user or not verify_password(user_data.password, user['password_hash']):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )
        
        if not user['is_verified']:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Email not verified. Please check your email and verify your account."
            )
        
        # Create access token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user['email'], "user_id": user['id']},
            expires_delta=access_token_expires
        )
        
        # Store session in database
        session_id = str(uuid.uuid4())
        expires_at = datetime.utcnow() + access_token_expires
        
        cursor.execute('''
            INSERT INTO login_sessions (id, user_id, token_hash, expires_at)
            VALUES (?, ?, ?, ?)
        ''', (session_id, user['id'], access_token[:50], expires_at))
        
        conn.commit()
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user_id": user['id']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Login failed: {str(e)}"
        )
    finally:
        conn.close()

@router.post("/logout")
async def logout_user(current_user: CurrentUser = Depends(get_current_user)):
    """
    Logout user by invalidating their session
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # Deactivate all sessions for this user
        cursor.execute(
            "UPDATE login_sessions SET is_active = FALSE WHERE user_id = ? AND is_active = TRUE",
            (current_user.id,)
        )
        conn.commit()
        
        return {"message": "Successfully logged out"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Logout failed: {str(e)}"
        )
    finally:
        conn.close()

@router.get("/me", response_model=CurrentUser)
async def get_user_profile(current_user: CurrentUser = Depends(get_current_user)):
    """
    Get current user profile
    """
    return current_user

@router.post("/refresh-token", response_model=Token)
async def refresh_token(current_user: CurrentUser = Depends(get_current_user)):
    """
    Refresh JWT token
    """
    # Create new access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": current_user.email, "user_id": current_user.id},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "user_id": current_user.id
    }
