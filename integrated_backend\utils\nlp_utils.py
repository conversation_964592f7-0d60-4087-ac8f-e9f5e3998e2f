"""
NLP Utilities for OpenResume AI
Provides keyword extraction, entity recognition, and text processing functions
"""

import re
import nltk
import spacy
from typing import List, Dict, Set, Tuple, Optional
from sklearn.feature_extraction.text import TfidfVectorizer
from collections import Counter
import pandas as pd

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

try:
    nltk.data.find('taggers/averaged_perceptron_tagger')
except LookupError:
    nltk.download('averaged_perceptron_tagger')

from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.tag import pos_tag

# Load spaCy model (using small English model)
try:
    nlp = spacy.load("en_core_web_sm")
except OSError:
    # If model not found, use blank model
    nlp = spacy.blank("en")

# Common technical skills and keywords
TECHNICAL_SKILLS = {
    'programming_languages': [
        'python', 'java', 'javascript', 'typescript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust',
        'swift', 'kotlin', 'scala', 'r', 'matlab', 'sql', 'html', 'css', 'bash', 'powershell'
    ],
    'frameworks': [
        'react', 'angular', 'vue', 'django', 'flask', 'fastapi', 'spring', 'express', 'nodejs',
        'laravel', 'rails', 'asp.net', 'tensorflow', 'pytorch', 'keras', 'scikit-learn'
    ],
    'databases': [
        'mysql', 'postgresql', 'mongodb', 'redis', 'elasticsearch', 'sqlite', 'oracle',
        'sql server', 'cassandra', 'dynamodb', 'firebase'
    ],
    'cloud_platforms': [
        'aws', 'azure', 'gcp', 'google cloud', 'heroku', 'digitalocean', 'kubernetes',
        'docker', 'terraform', 'ansible'
    ],
    'tools': [
        'git', 'github', 'gitlab', 'jenkins', 'jira', 'confluence', 'slack', 'teams',
        'figma', 'sketch', 'photoshop', 'illustrator'
    ]
}

# Common job roles and positions
JOB_ROLES = [
    'software engineer', 'software developer', 'full stack developer', 'frontend developer',
    'backend developer', 'data scientist', 'data analyst', 'machine learning engineer',
    'devops engineer', 'product manager', 'project manager', 'ui/ux designer',
    'web developer', 'mobile developer', 'qa engineer', 'test engineer',
    'system administrator', 'database administrator', 'security engineer',
    'technical lead', 'engineering manager', 'architect', 'consultant'
]

# Common soft skills
SOFT_SKILLS = [
    'leadership', 'communication', 'teamwork', 'problem solving', 'analytical thinking',
    'creativity', 'adaptability', 'time management', 'project management',
    'critical thinking', 'collaboration', 'mentoring', 'presentation skills'
]

def clean_text(text: str) -> str:
    """Clean and normalize text for processing"""
    if not text:
        return ""
    
    # Remove extra whitespace and normalize
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Remove special characters but keep alphanumeric and common punctuation
    text = re.sub(r'[^\w\s\-\.\,\;\:\!\?]', ' ', text)
    
    return text

def extract_keywords_tfidf(text: str, max_features: int = 50) -> List[Tuple[str, float]]:
    """Extract keywords using TF-IDF"""
    if not text:
        return []
    
    try:
        # Clean text
        cleaned_text = clean_text(text)
        
        # Use TF-IDF to extract keywords
        vectorizer = TfidfVectorizer(
            max_features=max_features,
            stop_words='english',
            ngram_range=(1, 2),  # Include bigrams
            min_df=1,
            max_df=0.95
        )
        
        tfidf_matrix = vectorizer.fit_transform([cleaned_text])
        feature_names = vectorizer.get_feature_names_out()
        scores = tfidf_matrix.toarray()[0]
        
        # Create keyword-score pairs and sort by score
        keywords = [(feature_names[i], scores[i]) for i in range(len(feature_names)) if scores[i] > 0]
        keywords.sort(key=lambda x: x[1], reverse=True)
        
        return keywords
    except Exception as e:
        print(f"Error in TF-IDF extraction: {e}")
        return []

def extract_technical_skills(text: str) -> Dict[str, List[str]]:
    """Extract technical skills from text"""
    if not text:
        return {}
    
    text_lower = text.lower()
    found_skills = {}
    
    for category, skills in TECHNICAL_SKILLS.items():
        found_in_category = []
        for skill in skills:
            # Use word boundaries to avoid partial matches
            pattern = r'\b' + re.escape(skill.lower()) + r'\b'
            if re.search(pattern, text_lower):
                found_in_category.append(skill)
        
        if found_in_category:
            found_skills[category] = found_in_category
    
    return found_skills

def extract_job_roles(text: str) -> List[str]:
    """Extract job roles and positions from text"""
    if not text:
        return []
    
    text_lower = text.lower()
    found_roles = []
    
    for role in JOB_ROLES:
        pattern = r'\b' + re.escape(role.lower()) + r'\b'
        if re.search(pattern, text_lower):
            found_roles.append(role)
    
    return found_roles

def extract_soft_skills(text: str) -> List[str]:
    """Extract soft skills from text"""
    if not text:
        return []
    
    text_lower = text.lower()
    found_skills = []
    
    for skill in SOFT_SKILLS:
        pattern = r'\b' + re.escape(skill.lower()) + r'\b'
        if re.search(pattern, text_lower):
            found_skills.append(skill)
    
    return found_skills

def extract_entities_spacy(text: str) -> Dict[str, List[str]]:
    """Extract named entities using spaCy"""
    if not text:
        return {}
    
    try:
        doc = nlp(text)
        entities = {}
        
        for ent in doc.ents:
            entity_type = ent.label_
            entity_text = ent.text.strip()
            
            if entity_type not in entities:
                entities[entity_type] = []
            
            if entity_text not in entities[entity_type]:
                entities[entity_type].append(entity_text)
        
        return entities
    except Exception as e:
        print(f"Error in spaCy entity extraction: {e}")
        return {}

def extract_education_info(text: str) -> Dict[str, List[str]]:
    """Extract education-related information"""
    if not text:
        return {}
    
    education_info = {}
    text_lower = text.lower()
    
    # Degrees
    degree_patterns = [
        r'\b(bachelor|master|phd|doctorate|associate|diploma|certificate)\b.*?\b(degree|of|in)\b.*?(?=\.|,|\n|$)',
        r'\b(b\.?s\.?|m\.?s\.?|m\.?a\.?|ph\.?d\.?|b\.?a\.?|m\.?b\.?a\.?)\b.*?(?=\.|,|\n|$)',
    ]
    
    degrees = []
    for pattern in degree_patterns:
        matches = re.findall(pattern, text_lower, re.IGNORECASE)
        degrees.extend([match if isinstance(match, str) else ' '.join(match) for match in matches])
    
    if degrees:
        education_info['degrees'] = degrees
    
    # Universities/Institutions
    university_patterns = [
        r'\b(university|college|institute|school)\s+of\s+\w+',
        r'\w+\s+(university|college|institute)',
    ]
    
    institutions = []
    for pattern in university_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        institutions.extend(matches)
    
    if institutions:
        education_info['institutions'] = institutions
    
    return education_info

def extract_experience_years(text: str) -> Optional[int]:
    """Extract years of experience from text"""
    if not text:
        return None
    
    # Patterns to match experience mentions
    patterns = [
        r'(\d+)\+?\s*years?\s*(?:of\s*)?experience',
        r'(\d+)\+?\s*years?\s*in',
        r'experience.*?(\d+)\+?\s*years?',
        r'(\d+)\+?\s*yrs?\s*(?:of\s*)?experience',
    ]
    
    years = []
    for pattern in patterns:
        matches = re.findall(pattern, text.lower())
        years.extend([int(match) for match in matches if match.isdigit()])
    
    return max(years) if years else None

def comprehensive_keyword_extraction(text: str) -> Dict[str, any]:
    """Perform comprehensive keyword and entity extraction"""
    if not text:
        return {}
    
    result = {
        'tfidf_keywords': extract_keywords_tfidf(text, max_features=30),
        'technical_skills': extract_technical_skills(text),
        'job_roles': extract_job_roles(text),
        'soft_skills': extract_soft_skills(text),
        'entities': extract_entities_spacy(text),
        'education': extract_education_info(text),
        'experience_years': extract_experience_years(text),
        'text_stats': {
            'word_count': len(text.split()),
            'sentence_count': len(sent_tokenize(text)),
            'character_count': len(text)
        }
    }
    
    return result
