"""
Database module for OpenResume AI
Handles all database operations for all user stories
"""

import sqlite3
import os
from typing import Optional
from datetime import datetime

# Database configuration
DB_PATH = os.path.join(os.path.dirname(__file__), "openresume.db")

def get_db_connection():
    """Get database connection with row factory"""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """Initialize all database tables for all user stories"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # US-01: Users table for registration
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                is_verified BOOLEAN DEFAULT FALSE,
                verification_token TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # US-02: Login sessions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS login_sessions (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                token_hash TEXT NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT TRUE,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # US-03: Resume uploads table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS resume_uploads (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                filename TEXT NOT NULL,
                original_filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_type TEXT NOT NULL,
                file_size INTEGER NOT NULL,
                upload_status TEXT DEFAULT 'uploaded',
                processed_text TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # US-04: Job description uploads table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS job_descriptions (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                title TEXT,
                company TEXT,
                description_text TEXT NOT NULL,
                original_filename TEXT,
                file_path TEXT,
                file_type TEXT,
                processed_text TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Analysis results table (for future features)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS analysis_results (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                resume_id TEXT,
                job_description_id TEXT,
                analysis_type TEXT NOT NULL,
                results_json TEXT,
                score REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (resume_id) REFERENCES resume_uploads (id),
                FOREIGN KEY (job_description_id) REFERENCES job_descriptions (id)
            )
        ''')
        
        conn.commit()
        print("Database tables created successfully")
        
    except Exception as e:
        print(f"Error initializing database: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

def create_indexes():
    """Create database indexes for better performance"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # Indexes for better query performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON login_sessions(user_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_sessions_token ON login_sessions(token_hash)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_resumes_user_id ON resume_uploads(user_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_jobs_user_id ON job_descriptions(user_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_analysis_user_id ON analysis_results(user_id)')
        
        conn.commit()
        print("Database indexes created successfully")
        
    except Exception as e:
        print(f"Error creating indexes: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    init_database()
    create_indexes()
    print(f"Database initialized at: {DB_PATH}")
