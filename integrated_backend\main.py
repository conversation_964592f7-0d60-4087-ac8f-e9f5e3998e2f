"""
OpenResume AI - Integrated Backend
Main FastAPI application for all user stories
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import os

# Import routers for each user story
from routers import (
    us01_auth, us02_login, us03_resume, us04_job_description,
    us05_keyword_extraction, us06_matching_score, us07_suggestions
)
from database.database import init_database


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    print("Starting OpenResume AI Backend...")
    init_database()
    print("Database initialized successfully")
    yield
    # Shutdown
    print("Shutting down OpenResume AI Backend...")


# Initialize FastAPI app
app = FastAPI(
    title="OpenResume AI - Integrated Backend",
    description="Complete backend service for OpenResume AI with all user stories",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers for each user story
app.include_router(us01_auth.router, prefix="/api/v1/auth", tags=["US-01: User Registration"])
app.include_router(us02_login.router, prefix="/api/v1/auth", tags=["US-02: Authentication"])
app.include_router(us03_resume.router, prefix="/api/v1/resume", tags=["US-03: Resume Upload"])
app.include_router(us04_job_description.router, prefix="/api/v1/job", tags=["US-04: Job Description"])
app.include_router(us05_keyword_extraction.router, prefix="/api/v1/keywords", tags=["US-05: Keyword Extraction"])
app.include_router(us06_matching_score.router, prefix="/api/v1/matching", tags=["US-06: Matching Score"])
app.include_router(us07_suggestions.router, prefix="/api/v1/suggestions", tags=["US-07: Suggestions"])

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "OpenResume AI - Integrated Backend",
        "version": "1.0.0",
        "services": [
            "US-01: User Registration",
            "US-02: Authentication",
            "US-03: Resume Upload",
            "US-04: Job Description Upload",
            "US-05: Keyword Extraction & Parsing",
            "US-06: Matching Score",
            "US-07: Suggestions"
        ]
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "OpenResume AI - Integrated Backend",
        "version": "1.0.0"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
